using System.Collections.Generic;
using AllGame.BallSort.Scripts.Gameplay.Tube;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using PathologicalGames;
using UnityEngine;

namespace AllGame.BallSort.Scripts.Gameplay
{
    public class AssetController : Bh<PERSON>ingleton<AssetController>
    {
        [SerializeField]
        private List<Sprite> ballSprites;
        
        [SerializeField]
        private Ball.BallController ballControllerPrefab;

        [SerializeField]
        private Transform ballContainer;
        
        [SerializeField]
        private TubeController tubeControllerPrefab;
        
        [SerializeField]
        private Transform tubeContainer;

        [SerializeField]
        private Sprite ballHiddenSprite;
        
        [SerializeField]
        private SerializedDictionary<int, Sprite> _ballSpriteMap = new();
        
        void Start()
        {
            InitBallPool();
            InitTubePool();
            ShuffleBall();
        }

        public Sprite GetBallSprite(int index)
        {
            if (_ballSpriteMap.TryGetValue(index, out Sprite sprite))
            {
                return sprite;
            }
            else
            {
                Debug.LogError($"Ball sprite index {index} is not found.");
                return null;
            }
        }

        public void ShuffleBall()
        {
            _ballSpriteMap.Clear();
            
            
            var allBalls = ballSprites.Clone();
            allBalls.Shuffle(null);
            
            for (var index = 0; index < allBalls.Count; index++)
            {
                _ballSpriteMap.TryAdd(index + 1, allBalls[index]);
            }
        }

        public Sprite GetBallHiddenSprite()
        {
            return ballHiddenSprite;
        }




        #region Pool

        private SpawnPool _ballPool;

        void InitBallPool() {
            _ballPool = PoolManager.Pools["BallSort_BallPool"];
        }

        public Ball.BallController GetBall() { 
            var newBall = _ballPool.Spawn(ballControllerPrefab.gameObject, ballContainer).GetComponent<Ball.BallController>();
            return newBall;
        }

        public void DespawnBall(Ball.BallController ballController) {
            _ballPool.Despawn(ballController.transform);
        }

        public void DespawnAllBalls() {
            _ballPool.DespawnAll();
        }

        #region Pool Ball


        #endregion


        #region Pool Tube
        
        
        private SpawnPool _tubePool;

        void InitTubePool() {
            _tubePool = PoolManager.Pools["BallSort_TubePool"];
        }

        public TubeController GetTube() {
            var newTube = _tubePool.Spawn(tubeControllerPrefab.gameObject, tubeContainer).GetComponent<TubeController>();
            return newTube;
        }

        public void DespawnTube(TubeController tubeController) {
            _tubePool.Despawn(tubeController.transform);
        }

        public void DespawnAllTubes() {
            _tubePool.DespawnAll();
        }

        #endregion
        

        #endregion
    }
}