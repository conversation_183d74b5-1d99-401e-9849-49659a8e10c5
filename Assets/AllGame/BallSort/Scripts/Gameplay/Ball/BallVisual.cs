using b100SDK.Scripts.Base;
using UnityEngine;

namespace AllGame.BallSort.Scripts.Gameplay.Ball
{
    public class BallVisual : BhMonoBehavior
    {
        [SerializeField]
        private SpriteRenderer spriteRenderer;

        public void Init(int value, bool isHidden)
        {
            if (!isHidden)
            {
                spriteRenderer.sprite = AssetController.Instance.GetBallSprite(value);
            }
            else
            {
                spriteRenderer.sprite = AssetController.Instance.GetBallHiddenSprite();
            }
        }

        public void ShowBall(int value)
        {
            spriteRenderer.sprite = AssetController.Instance.GetBallSprite(value);
        }
    }
}