using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using UnityEngine;

namespace AllGame.BallSort.Scripts.Gameplay.Tube
{
    public class TubeController : BhMonoBehavior
    {
        [SerializeField]
        private List<Transform> ballTransforms;
        
        [SerializeField]
        private SpriteRenderer tubeRenderer;
        
        [SerializeField]
        private TubeInteraction tubeInteraction;
        
        [SerializeField]
        private Transform ballStartTransform;

        [SerializeField]
        private float selectDuration = .2f;
        
        [SerializeField]
        private float deselectDuration = .2f;

        private List<Ball.BallController> _balls = new();

        private bool _isComplete;
        private bool _isHidden;
        public bool IsComplete => _isComplete;

        public void Init(List<int> data, Action<TubeController> onClickTube, GameMode gameMode)
        {
            _isComplete = false;
            _isHidden = gameMode == GameMode.Hidden;
            SpawnBall(data, _isHidden);
            tubeInteraction.Init(this, onClickTube);
        }
        


        void SpawnBall(List<int> data, bool isHidden)
        {
            _balls.Clear();
            
            data.Reverse();
            
            for (var index = 0; index < data.Count; index++)
            {
                var ballValue = data[index];
                
                if (ballValue == 0)
                    break;
                
                var ball = AssetController.Instance.GetBall();
                
                ball.transform.SetParent(transform);

                ball.transform.localPosition = GetLocalPositionForIndex(index);
                
                ball.Init(data[index], index < data.Count - 1  && isHidden);
                
                _balls.Add(ball);
            }
        }


        public Vector3 GetLocalPositionForIndex(int index)
        {
            if (index >= 0 && index < ballTransforms.Count)
            {
                return ballTransforms[index].localPosition;
            }
            else
            {
                Debug.LogError($"Index {index} is out of range for ball transforms.");
                return Vector3.zero;
            }
        }
        
        
        public Bounds GetBounds()
        {
            return tubeRenderer.bounds;
        }


        public void SelectVisual(bool instant = false)
        {
            // Di chuyển bóng đến vị trí move
            var lastBall = _balls.GetLast();
            
            if (!lastBall)
                return;

            if (instant)
            {
                lastBall.transform.position = ballStartTransform.position;
            }
            else
            {
                lastBall.MoveTo(ballStartTransform.position, selectDuration);
            }
        }
        
        public void DeselectVisual(bool instant = false)
        {
            var lastBall = _balls.GetLast();
            
            if (!lastBall)
                return;

            var targetTransform = ballTransforms[_balls.Count - 1];

            if (instant)
            {
                lastBall.transform.position = targetTransform.position;
            }
            else
            {
                lastBall.MoveTo(targetTransform.position, deselectDuration);
            }

        }


        public List<Ball.BallController> GetBallsCanMove(out int ballValue)
        {
            var result = new List<Ball.BallController>();

            if (_balls.Count <= 0)
            {
                ballValue = 0;
                return result;
            }
            
            var lastBall = _balls[^1];

            for (int i = _balls.Count - 1; i >= 0; i--)
            {
                if (_balls[i].Value == lastBall.Value && !_balls[i].IsHidden)
                {
                    result.Add(_balls[i]);
                }
                else
                {
                    break;
                }
            }

            ballValue = lastBall.Value;
            return result;
        }
        
        
        public int GetBallCount()
        {
            return _balls.Count;
        }

        public List<Ball.BallController> GetBalls()
        {
            return _balls;
        }

        public int GetNumberBallCanAdd(out int ballValue)
        {
            var maxBallCanAdd = ballTransforms.Count - _balls.Count;
            
            var lastBallValue = _balls.Count > 0 ? _balls[^1].Value : 0;
            ballValue = lastBallValue;
            return maxBallCanAdd;
        }
        
        
        private void CheckComplete()
        {
            _isComplete = _balls.Count == 0 ||
                          (_balls.Count == ballTransforms.Count && _balls.All(x => x.Value == _balls[0].Value));
            
            BhDebug.Log($"Is complete: {_isComplete}");

            if (_isComplete)
            {
                BhDebug.Log("Complete");
                tubeInteraction.CanInteractive = _balls.Count <= 0;
            }
        }
        
        public void AddBall(Ball.BallController ballController)
        {
            if (_balls.Count < ballTransforms.Count)
            {
                _balls.Add(ballController);
                ballController.transform.SetParent(transform);
            }
            else
            {
                BhDebug.LogError("Cannot add more balls, tube is full.");
            }
        }


        public void AddBall(List<Ball.BallController> balls)
        {
            foreach (var ball in balls)
            {
                AddBall(ball);
            }
            
            CheckComplete();
        }
        
        public void RemoveBall(Ball.BallController ballController)
        {
            if (_balls.Contains(ballController))
            {
                _balls.Remove(ballController);
                if (_isHidden)
                {
                    var lastBall = _balls.GetLast();
                    if (lastBall)
                    {
                        BhDebug.Log("Show visual last ball");
                        lastBall.ShowBall();
                    }
                    else
                    {
                        BhDebug.Log("Not found last ball");
                    }
                }
            }
            else
            {
                BhDebug.LogError("Ball not found in tube.");
            }
        }
        
        public void RemoveBall(List<Ball.BallController> balls)
        {
            foreach (var ball in balls)
            {
                RemoveBall(ball);
            }
            
            CheckComplete();
        }
        
        
        public Transform GetBallStartTransform()
        {
            return ballStartTransform;
        }

        public Transform GetBallTransform(int index)
        {
            return ballTransforms[index];
        }
        
        public Transform GetBallTransform(Ball.BallController ballController)
        {
            return GetBallTransform(_balls.IndexOf(ballController));
        }


        public bool CanSelect()
        {
            return GetBallCount() > 0 && !_isComplete;
        }
        
    }
}