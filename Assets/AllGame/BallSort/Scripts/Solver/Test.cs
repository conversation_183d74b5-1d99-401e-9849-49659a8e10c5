using System;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using Sirenix.OdinInspector;

namespace WaterSortSolver
{
    public class Test : BhMonoBehavior
    {
        [Button]
        void Solver()
        {
            var api = new WaterSortAPI();
            var config = new WaterSortAPI.PuzzleConfig 
            { 
                Colors = 3, 
                EmptyVials = 2, 
                VialVolume = 3 
            };

// Tạo puzzle dạng List<List<int>>
            var puzzle = new List<List<int>>
            {
                new List<int> { 3, 2, 1 },    // Vial 1
                new List<int> { 1, 3, 2 },    // Vial 2  
                new List<int> { 2, 1, 3 },    // Vial 3
                new List<int> { 0, 0, 0 },    // Vial 4 (empty)
                new List<int> { 0, 0, 0 }     // Vial 5 (empty)
            };

// Giải puzzle
            var result = api.SolveOptimalFromList(puzzle, config);
            if (result.Success)
            {
                Console.WriteLine($"Solved in {result.Moves.Count} moves");
                foreach (var move in result.Moves)
                    Console.WriteLine($"Move: {move}");
            }

// Thực hiện nước đi
            var newState = api.ApplyMoveToList(puzzle, 0, 3, config); // Di chuyển từ vial 1 -> vial 4
        }
    }
}