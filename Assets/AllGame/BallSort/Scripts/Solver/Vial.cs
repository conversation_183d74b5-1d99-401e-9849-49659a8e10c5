using System;

namespace WaterSortSolver
{
    public class Vial
    {
        public ColorType[] color;
        public byte pos;

        public Vial()
        {
            color = new ColorType[10]; // Default max volume
            pos = 0;
        }

        public Vial(ColorType[] c, byte p, int nvolume)
        {
            color = new ColorType[nvolume];
            for (int i = 0; i < nvolume; i++)
                color[i] = c[i];
            pos = p;
        }

        public VialTopInfo GetTopInfo(int nvolume)
        {
            VialTopInfo result = new VialTopInfo
            {
                topcol = 0,
                empty = nvolume,
                topvol = 0
            };

            if (color[nvolume - 1] == ColorType.EMPTY)
                return result; // empty vial

            for (int i = 0; i < nvolume; i++)
            {
                if (color[i] != ColorType.EMPTY)
                {
                    int cl = (int)color[i];
                    result.topcol = cl;
                    result.empty = i;
                    break;
                }
            }

            result.topvol = 1;
            for (int i = result.empty + 1; i < nvolume; i++)
            {
                if ((int)color[i] == result.topcol)
                    result.topvol++;
                else
                    break;
            }

            return result;
        }

        public int VialBlocks(int nvolume)
        {
            int result = 1;
            for (int i = 0; i < nvolume - 1; i++)
            {
                if (color[i + 1] != color[i])
                    result++;
            }
            if (color[0] == ColorType.EMPTY)
                result--;
            return result;
        }
    }
} 