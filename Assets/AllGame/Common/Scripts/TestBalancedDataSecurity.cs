using UnityEngine;
using Sirenix.OdinInspector;
using System;
using System.Diagnostics;

namespace AllGame.Common.Scripts
{
    public class TestBalancedDataSecurity : MonoBehaviour
    {
        [SerializeField] private string testPassword = "GamePass123!";
        [SerializeField] private string testData = "Player: MobileGamer, Level: 99, Coins: 999999, Items: [legendary_sword, dragon_shield, health_potion_x50]";
        
        [Header("Results")]
        [SerializeField, TextArea(2, 4)] private string balancedResult = "";
        [SerializeField, TextArea(2, 4)] private string gameSpecificResult = "";
        [SerializeField, TextArea(2, 4)] private string simpleResult = "";

        [But<PERSON>("Test All Methods")]
        public void TestAllMethods()
        {
            var sw = Stopwatch.StartNew();
            
            // Test Balanced Crypto
            sw.Restart();
            balancedResult = BalancedCrypto.Encrypt(testData, testPassword);
            var balancedEncryptTime = sw.ElapsedTicks;
            
            sw.Restart();
            string balancedDecrypted = BalancedCrypto.Decrypt(balancedResult, testPassword);
            var balancedDecryptTime = sw.ElapsedTicks;
            
            // Test Game Specific Crypto
            sw.Restart();
            gameSpecificResult = GameSpecificCrypto.EncryptGameData(testData, testPassword);
            var gameEncryptTime = sw.ElapsedTicks;
            
            sw.Restart();
            string gameDecrypted = GameSpecificCrypto.DecryptGameData(gameSpecificResult, testPassword);
            var gameDecryptTime = sw.ElapsedTicks;
            
            // Test Simple (from previous)
            sw.Restart();
            simpleResult = GameDataCrypto.SimpleEncrypt(testData, testPassword);
            var simpleEncryptTime = sw.ElapsedTicks;
            
            sw.Restart();
            string simpleDecrypted = GameDataCrypto.SimpleDecrypt(simpleResult, testPassword);
            var simpleDecryptTime = sw.ElapsedTicks;
            
            // Results
            UnityEngine.Debug.Log("=== ENCRYPTION COMPARISON ===");
            UnityEngine.Debug.Log($"Balanced: Encrypt {balancedEncryptTime} ticks, Decrypt {balancedDecryptTime} ticks");
            UnityEngine.Debug.Log($"Game Specific: Encrypt {gameEncryptTime} ticks, Decrypt {gameDecryptTime} ticks");
            UnityEngine.Debug.Log($"Simple XOR: Encrypt {simpleEncryptTime} ticks, Decrypt {simpleDecryptTime} ticks");
            
            UnityEngine.Debug.Log($"\nDecryption Success:");
            UnityEngine.Debug.Log($"Balanced: {balancedDecrypted == testData}");
            UnityEngine.Debug.Log($"Game Specific: {gameDecrypted == testData}");
            UnityEngine.Debug.Log($"Simple: {simpleDecrypted == testData}");
            
            UnityEngine.Debug.Log($"\nEncrypted Size Comparison:");
            UnityEngine.Debug.Log($"Balanced: {balancedResult.Length} chars");
            UnityEngine.Debug.Log($"Game Specific: {gameSpecificResult.Length} chars");
            UnityEngine.Debug.Log($"Simple: {simpleResult.Length} chars");
        }

        [Button("Mobile Performance Test")]
        public void MobilePerformanceTest()
        {
            const int iterations = 1000;
            var gameData = new MobileGameData 
            { 
                PlayerId = "player_12345",
                Level = 50,
                Experience = 125000,
                Coins = 999999,
                Items = new string[] { "sword", "shield", "potion", "bow", "armor" },
                Settings = new GameSettings { SoundEnabled = true, MusicVolume = 0.8f }
            };
            
            var sw = Stopwatch.StartNew();
            
            // Test Balanced
            sw.Restart();
            for (int i = 0; i < iterations; i++)
            {
                gameData.Level = i % 100 + 1;
                string encrypted = BalancedCrypto.EncryptJson(gameData, testPassword);
                var decrypted = BalancedCrypto.DecryptJson<MobileGameData>(encrypted, testPassword);
            }
            var balancedTime = sw.ElapsedMilliseconds;
            
            // Test Game Specific
            sw.Restart();
            for (int i = 0; i < iterations; i++)
            {
                gameData.Level = i % 100 + 1;
                string json = JsonUtility.ToJson(gameData);
                string encrypted = GameSpecificCrypto.EncryptGameData(json, testPassword);
                string decryptedJson = GameSpecificCrypto.DecryptGameData(encrypted, testPassword);
                var decrypted = JsonUtility.FromJson<MobileGameData>(decryptedJson);
            }
            var gameSpecificTime = sw.ElapsedMilliseconds;
            
            // Test Simple
            sw.Restart();
            for (int i = 0; i < iterations; i++)
            {
                gameData.Level = i % 100 + 1;
                string json = JsonUtility.ToJson(gameData);
                string encrypted = GameDataCrypto.SimpleEncrypt(json, testPassword);
                string decryptedJson = GameDataCrypto.SimpleDecrypt(encrypted, testPassword);
                var decrypted = JsonUtility.FromJson<MobileGameData>(decryptedJson);
            }
            var simpleTime = sw.ElapsedMilliseconds;
            
            UnityEngine.Debug.Log("=== MOBILE PERFORMANCE TEST ===");
            UnityEngine.Debug.Log($"{iterations} iterations:");
            UnityEngine.Debug.Log($"Balanced: {balancedTime}ms ({balancedTime / (float)iterations:F3}ms per operation)");
            UnityEngine.Debug.Log($"Game Specific: {gameSpecificTime}ms ({gameSpecificTime / (float)iterations:F3}ms per operation)");
            UnityEngine.Debug.Log($"Simple XOR: {simpleTime}ms ({simpleTime / (float)iterations:F3}ms per operation)");
            
            // Recommendations
            if (balancedTime < 50)
                UnityEngine.Debug.Log("✅ BALANCED: Perfect for mobile - good security + speed");
            if (gameSpecificTime < 30)
                UnityEngine.Debug.Log("✅ GAME SPECIFIC: Excellent for mobile gaming");
            if (simpleTime < 20)
                UnityEngine.Debug.Log("✅ SIMPLE: Ultra fast but lower security");
                
UnityEngine.Debug.Log($"\n🎯 RECOMMENDATION: Use {(balancedTime < 100 ? "Balanced" : gameSpecificTime < 50 ? "GameSpecific" : "Simple")} for best balance");;
        }

        [Button("Security Analysis")]
        public void SecurityAnalysis()
        {
            UnityEngine.Debug.Log("=== SECURITY ANALYSIS ===");
            
            // Test with same data - should produce different outputs
            string test1_balanced = BalancedCrypto.Encrypt("test", testPassword);
            string test2_balanced = BalancedCrypto.Encrypt("test", testPassword);
            
            string test1_game = GameSpecificCrypto.EncryptGameData("test", testPassword);
            string test2_game = GameSpecificCrypto.EncryptGameData("test", testPassword);
            
            string test1_simple = GameDataCrypto.SimpleEncrypt("test", testPassword);
            string test2_simple = GameDataCrypto.SimpleEncrypt("test", testPassword);
            
            UnityEngine.Debug.Log($"Balanced - Same input produces different output: {test1_balanced != test2_balanced} ✅");
            UnityEngine.Debug.Log($"Game Specific - Same input produces same output: {test1_game == test2_game} ⚠️");
            UnityEngine.Debug.Log($"Simple XOR - Same input produces same output: {test1_simple == test2_simple} ⚠️");
            
            // Test wrong password
            try
            {
                BalancedCrypto.Decrypt(test1_balanced, "wrong_password");
                UnityEngine.Debug.Log("Balanced - Wrong password: Graceful failure ✅");
            }
            catch
            {
                UnityEngine.Debug.Log("Balanced - Wrong password: Exception thrown ⚠️");
            }
            
            UnityEngine.Debug.Log("\n🔒 SECURITY RANKING:");
            UnityEngine.Debug.Log("1. Balanced (AES-CTR) - High security");
            UnityEngine.Debug.Log("2. Game Specific - Medium security");
            UnityEngine.Debug.Log("3. Simple XOR - Low security");
        }

        private void OnDestroy()
        {
            BalancedCrypto.ClearCache();
        }
    }

    [Serializable]
    public class MobileGameData
    {
        public string PlayerId;
        public int Level;
        public long Experience;
        public int Coins;
        public string[] Items;
        public GameSettings Settings;
    }

    [Serializable]
    public class GameSettings
    {
        public bool SoundEnabled;
        public float MusicVolume;
    }
}