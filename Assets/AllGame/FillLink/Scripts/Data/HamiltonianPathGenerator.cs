
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using b100SDK.Scripts.Utilities;

public class HamiltonianPathGenerator
{
    private Random random;
    
    public HamiltonianPathGenerator(int seed = -1)
    {
        random = seed == -1 ? new Random() : new Random(seed);
    }
    
    /// <summary>
    /// Tạo Hamiltonian path với kích thước m×n và tỷ lệ lấp đầy
    /// </summary>
    /// <param name="m"><PERSON><PERSON> hàng</param>
    /// <param name="n"><PERSON>ố cột</param>
    /// <param name="fillRatio">Tỷ lệ lấp đầy (0.0 - 1.0)</param>
    /// <returns>Tuple chứa mảng 2D và danh sách path theo index</returns>
    public (int[,] grid, List<(int row, int col)> path) GenerateHamiltonianPath(int m, int n, double fillRatio)
    {
        if (m <= 0 || n <= 0)
            throw new ArgumentException("<PERSON><PERSON><PERSON> thước phải lớn hơn 0");
        
        if (fillRatio < 0.0 || fillRatio > 1.0)
            throw new ArgumentException("Tỷ lệ lấp đầy phải trong khoảng [0.0, 1.0]");
        
        int totalCells = m * n;
        int targetCells = Math.Max(1, (int)(totalCells * fillRatio));
        
        // Khởi tạo grid
        int[,] grid = new int[m, n];
        List<(int row, int col)> path = new List<(int, int)>();
        
        // Thử tạo path nhiều lần để tăng khả năng thành công
        for (int attempt = 0; attempt < 100; attempt++)
        {
            if (TryGeneratePath(m, n, targetCells, out grid, out path))
            {
                return (grid, path);
            }
        }
        
        // Nếu không tạo được Hamiltonian path, tạo path đơn giản
        return GenerateSimplePath(m, n, targetCells);
    }

    /// <summary>
    /// Tạo Hamiltonian path bất đồng bộ với kích thước m×n và tỷ lệ lấp đầy
    /// </summary>
    /// <param name="m">Số hàng</param>
    /// <param name="n">Số cột</param>
    /// <param name="fillRatio">Tỷ lệ lấp đầy (0.0 - 1.0)</param>
    /// <returns>Task chứa Tuple với mảng 2D và danh sách path</returns>
    public async Task<(int[,] grid, List<(int row, int col)> path)> GenerateHamiltonianPathAsync(int m, int n, double fillRatio)
    {
        if (m <= 0 || n <= 0)
            throw new ArgumentException("Kích thước phải lớn hơn 0");
        
        if (fillRatio < 0.0 || fillRatio > 1.0)
            throw new ArgumentException("Tỷ lệ lấp đầy phải trong khoảng [0.0, 1.0]");
        
        int totalCells = m * n;
        int targetCells = Math.Max(1, (int)(totalCells * fillRatio));
        
        // Khởi tạo grid
        int[,] grid = new int[m, n];
        List<(int row, int col)> path = new List<(int, int)>();
        
        // Thử tạo path nhiều lần để tăng khả năng thành công
        for (int attempt = 0; attempt < 100; attempt++)
        {
            var result = await TryGeneratePathAsync(m, n, targetCells);
            if (result.isSuccess)
            {
                return (result.grid, result.path);
            }
            /*if (TryGeneratePath(m, n, targetCells, out grid, out path))
            {
                return (grid, path);
            }*/
            
            BhDebug.Log($"Tạo path thất bại, thử lại lần {attempt + 1}");
            await Task.Delay(100);
        }
        
        // Nếu không tạo được Hamiltonian path, tạo path đơn giản
        BhDebug.Log("Tạo path thất bại, sử dụng path đơn giản");
        return GenerateSimplePath(m, n, targetCells);
    }

    /// <summary>
    /// Tạo nhiều Hamiltonian paths bất đồng bộ
    /// </summary>
    /// <param name="count">Số lượng paths cần tạo</param>
    /// <param name="m">Số hàng</param>
    /// <param name="n">Số cột</param>
    /// <param name="fillRatio">Tỷ lệ lấp đầy</param>
    /// <param name="progressCallback">Callback để báo cáo tiến độ</param>
    /// <returns>Danh sách các paths đã tạo</returns>
    public async Task<List<(int[,] grid, List<(int row, int col)> path)>> GenerateMultiplePathsAsync(
        int count, int m, int n, double fillRatio, 
        Action<int, int> progressCallback = null)
    {
        var results = new List<(int[,] grid, List<(int row, int col)> path)>();
        
        for (int i = 0; i < count; i++)
        {
            // Tạo path bất đồng bộ
            var result = await GenerateHamiltonianPathAsync(m, n, fillRatio);
            results.Add(result);
            
            // Báo cáo tiến độ
            progressCallback?.Invoke(i + 1, count);
            
            // Yield để không block UI thread
            await Task.Delay(1);
        }
        
        return results;
    }

    /// <summary>
    /// Tạo Hamiltonian path với timeout
    /// </summary>
    /// <param name="m">Số hàng</param>
    /// <param name="n">Số cột</param>
    /// <param name="fillRatio">Tỷ lệ lấp đầy</param>
    /// <param name="timeoutMs">Timeout tính bằng milliseconds</param>
    /// <returns>Task chứa kết quả hoặc null nếu timeout</returns>
    public async Task<(int[,] grid, List<(int row, int col)> path)?> GenerateHamiltonianPathWithTimeoutAsync(
        int m, int n, double fillRatio, int timeoutMs = 5000)
    {
        try
        {
            var task = GenerateHamiltonianPathAsync(m, n, fillRatio);
            var delayTask = Task.Delay(timeoutMs);
        
            var completedTask = await Task.WhenAny(task, delayTask);
        
            if (completedTask == task)
            {
                return await task;
            }
            else
            {
                BhDebug.LogWarning($"Timeout khi tạo Hamiltonian path {m}x{n} với fillRatio {fillRatio}");
                return null;
            }
        }
        catch (Exception ex)
        {
            BhDebug.LogError($"Lỗi khi tạo Hamiltonian path: {ex.Message}");
            return null;
        }

    }

    /// <summary>
    /// Tạo path với nhiều tùy chọn khác nhau và chọn tốt nhất
    /// </summary>
    /// <param name="m">Số hàng</param>
    /// <param name="n">Số cột</param>
    /// <param name="fillRatio">Tỷ lệ lấp đầy</param>
    /// <param name="optionCount">Số lượng tùy chọn cần tạo</param>
    /// <returns>Path tốt nhất</returns>
    public async Task<(int[,] grid, List<(int row, int col)> path, double score)> GenerateBestPathAsync(
        int m, int n, double fillRatio, int optionCount = 5)
    {
        var tasks = new List<Task<(int[,] grid, List<(int row, int col)> path)>>();
        
        // Tạo nhiều tasks song song
        for (int i = 0; i < optionCount; i++)
        {
            tasks.Add(GenerateHamiltonianPathAsync(m, n, fillRatio));
        }
        
        // Chờ tất cả tasks hoàn thành
        var results = await Task.WhenAll(tasks);
        
        // Đánh giá và chọn path tốt nhất
        var bestResult = results[0];
        double bestScore = EvaluatePath(bestResult.path);
        
        for (int i = 1; i < results.Length; i++)
        {
            double score = EvaluatePath(results[i].path);
            if (score > bestScore)
            {
                bestScore = score;
                bestResult = results[i];
            }
        }
        
        return (bestResult.grid, bestResult.path, bestScore);
    }

    /// <summary>
    /// Đánh giá chất lượng của một path (có thể customize)
    /// </summary>
    private double EvaluatePath(List<(int row, int col)> path)
    {
        if (path == null || path.Count == 0) return 0;
        
        double score = path.Count; // Điểm cơ bản dựa trên độ dài
        
        // Bonus cho path có nhiều hướng thay đổi (đa dạng hơn)
        int directionChanges = 0;
        for (int i = 2; i < path.Count; i++)
        {
            var prev = path[i - 2];
            var curr = path[i - 1];
            var next = path[i];
            
            var dir1 = (curr.row - prev.row, curr.col - prev.col);
            var dir2 = (next.row - curr.row, next.col - curr.col);
            
            if (dir1 != dir2)
                directionChanges++;
        }
        
        score += directionChanges * 0.5; // Bonus nhỏ cho diversity
        
        return score;
    }

    /// <summary>
    /// Batch processing - tạo nhiều levels bất đồng bộ
    /// </summary>
    /// <param name="levelConfigs">Danh sách cấu hình cho từng level</param>
    /// <param name="progressCallback">Callback báo cáo tiến độ</param>
    /// <param name="maxConcurrency">Số lượng tasks chạy song song tối đa</param>
    /// <returns>Danh sách kết quả</returns>
    public async Task<List<(int levelIndex, int[,] grid, List<(int row, int col)> path)>> GenerateLevelsAsync(
        List<(int levelIndex, int m, int n, double fillRatio)> levelConfigs,
        Action<int, int> progressCallback = null,
        int maxConcurrency = 3)
    {
        var results = new List<(int levelIndex, int[,] grid, List<(int row, int col)> path)>();
        var semaphore = new System.Threading.SemaphoreSlim(maxConcurrency, maxConcurrency);
        
        var tasks = levelConfigs.Select(async config =>
        {
            await semaphore.WaitAsync();
            try
            {
                var result = await GenerateHamiltonianPathAsync(config.m, config.n, config.fillRatio);
                
                lock (results)
                {
                    results.Add((config.levelIndex, result.grid, result.path));
                    progressCallback?.Invoke(results.Count, levelConfigs.Count);
                }
                
                return (config.levelIndex, result.grid, result.path);
            }
            finally
            {
                semaphore.Release();
            }
        });
        
        await Task.WhenAll(tasks);
        
        // Sắp xếp theo levelIndex
        return results.OrderBy(r => r.levelIndex).ToList();
    }
    
    private bool TryGeneratePath(int m, int n, int targetCells, out int[,] grid, out List<(int row, int col)> path)
    {
        grid = new int[m, n];
        path = new List<(int, int)>();
        bool[,] visited = new bool[m, n];
        
        // Chọn điểm bắt đầu ngẫu nhiên
        int startRow = random.Next(m);
        int startCol = random.Next(n);
        
        return Dfs(grid, visited, path, startRow, startCol, m, n, targetCells, 0);
    }    
    
    private async Task<(bool isSuccess, int[,] grid, List<(int row, int col)> path)> TryGeneratePathAsync(int m, int n, int targetCells)
    {
        
        var grid = new int[m, n];
        var path = new List<(int, int)>();
        bool[,] visited = new bool[m, n];
        
        // Chọn điểm bắt đầu ngẫu nhiên
        int startRow = random.Next(m);
        int startCol = random.Next(n);

        var result = await DfsAsync(grid, visited, path, startRow, startCol, m, n, targetCells, 0);
        return (result, grid, path);
    }
    
    private bool Dfs(int[,] grid, bool[,] visited, List<(int row, int col)> path, 
                    int row, int col, int m, int n, int targetCells, int currentCount)
    {
        if (currentCount == targetCells)
        {
            // Đã đủ số ô cần thiết
            return true;
        }
        
        if (row < 0 || row >= m || col < 0 || col >= n || visited[row, col])
        {
            return false;
        }
        
        // Đánh dấu ô hiện tại
        visited[row, col] = true;
        grid[row, col] = 1;
        path.Add((row, col));
        
        // Danh sách các hướng di chuyển (lên, xuống, trái, phải)
        int[] deltaRow = { -1, 1, 0, 0 };
        int[] deltaCol = { 0, 0, -1, 1 };
        
        // Tạo danh sách các hướng ngẫu nhiên
        var directions = Enumerable.Range(0, 4).OrderBy(x => random.Next()).ToArray();
        
        foreach (int dir in directions)
        {
            int newRow = row + deltaRow[dir];
            int newCol = col + deltaCol[dir];
            
            if (Dfs(grid, visited, path, newRow, newCol, m, n, targetCells, currentCount + 1))
            {
                return true;
            }
        }
        
        // Backtrack
        visited[row, col] = false;
        grid[row, col] = 0;
        path.RemoveAt(path.Count - 1);
        
        return false;
    }    
    
    
    private async Task<bool> DfsAsync(int[,] grid, bool[,] visited, List<(int row, int col)> path, 
                    int row, int col, int m, int n, int targetCells, int currentCount)
    {
        if (currentCount == targetCells)
        {
            // Đã đủ số ô cần thiết
            return true;
        }
        
        if (row < 0 || row >= m || col < 0 || col >= n || visited[row, col])
        {
            return false;
        }
        
        // Đánh dấu ô hiện tại
        visited[row, col] = true;
        grid[row, col] = 1;
        path.Add((row, col));
        
        // Danh sách các hướng di chuyển (lên, xuống, trái, phải)
        int[] deltaRow = { -1, 1, 0, 0 };
        int[] deltaCol = { 0, 0, -1, 1 };
        
        // Tạo danh sách các hướng ngẫu nhiên
        var directions = Enumerable.Range(0, 4).OrderBy(x => random.Next()).ToArray();
        
        foreach (int dir in directions)
        {
            int newRow = row + deltaRow[dir];
            int newCol = col + deltaCol[dir];
            
            var dfsTask = await DfsAsync(grid, visited, path, newRow, newCol, m, n, targetCells, currentCount + 1);
            
            if (dfsTask)
            {
                return true;
            }
            
            await Task.Delay(10);
        }
        
        // Backtrack
        visited[row, col] = false;
        grid[row, col] = 0;
        path.RemoveAt(path.Count - 1);
        
        return false;
    }
    
    private (int[,] grid, List<(int row, int col)> path) GenerateSimplePath(int m, int n, int targetCells)
    {
        int[,] grid = new int[m, n];
        List<(int row, int col)> path = new List<(int, int)>();
        
        // Tạo path theo pattern zic-zac
        List<(int, int)> allPositions = new List<(int, int)>();
        
        for (int i = 0; i < m; i++)
        {
            if (i % 2 == 0)
            {
                // Hàng chẵn: đi từ trái sang phải
                for (int j = 0; j < n; j++)
                {
                    allPositions.Add((i, j));
                }
            }
            else
            {
                // Hàng lẻ: đi từ phải sang trái
                for (int j = n - 1; j >= 0; j--)
                {
                    allPositions.Add((i, j));
                }
            }
        }
        
        // Lấy số lượng ô cần thiết
        for (int i = 0; i < Math.Min(targetCells, allPositions.Count); i++)
        {
            var pos = allPositions[i];
            grid[pos.Item1, pos.Item2] = 1;
            path.Add(pos);
        }
        
        return (grid, path);
    }
    
    /// <summary>
    /// In grid ra console để debug
    /// </summary>
    public void PrintGrid(int[,] grid)
    {
        int m = grid.GetLength(0);
        int n = grid.GetLength(1);
        
        var sb = new StringBuilder();
        sb.AppendLine($"Grid {m}x{n}:");
        for (int i = 0; i < m; i++)
        {
            for (int j = 0; j < n; j++)
            {
                sb.Append(grid[i, j] + " ");
            }
            sb.AppendLine();
        }
        
        BhDebug.Log(sb.ToString());
    }
    
    /// <summary>
    /// In path ra console để debug
    /// </summary>
    public void PrintPath(List<(int row, int col)> path)
    {
        var sb = new StringBuilder();
        sb.AppendLine($"Path (length: {path.Count}):");
        for (int i = 0; i < path.Count; i++)
        {
            sb.Append($"({path[i].row},{path[i].col})");
            if (i < path.Count - 1) sb.Append(" -> ");
        }
        sb.AppendLine();
        
        BhDebug.Log(sb.ToString());
    }
    
    /// <summary>
    /// Kiểm tra tính hợp lệ của path (các ô liền kề nhau)
    /// </summary>
    public bool ValidatePath(List<(int row, int col)> path)
    {
        if (path.Count <= 1) return true;
        
        for (int i = 0; i < path.Count - 1; i++)
        {
            var current = path[i];
            var next = path[i + 1];
            
            int rowDiff = Math.Abs(current.row - next.row);
            int colDiff = Math.Abs(current.col - next.col);
            
            // Kiểm tra các ô có liền kề nhau không (chỉ cho phép di chuyển lên/xuống/trái/phải)
            if (!((rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1)))
            {
                return false;
            }
        }
        
        return true;
    }

    public int[,] GetCorrectGrid(int[,] originalGrid)
    {
        if (originalGrid == null)
            return null;
    
        int rows = originalGrid.GetLength(0);
        int cols = originalGrid.GetLength(1);
    
        // Tìm các hàng có ít nhất 1 giá trị khác 0
        List<int> validRows = new List<int>();
        for (int i = 0; i < rows; i++)
        {
            bool hasNonZero = false;
            for (int j = 0; j < cols; j++)
            {
                if (originalGrid[i, j] != 0)
                {
                    hasNonZero = true;
                    break;
                }
            }
            if (hasNonZero)
                validRows.Add(i);
        }
    
        // Tìm các cột có ít nhất 1 giá trị khác 0
        List<int> validCols = new List<int>();
        for (int j = 0; j < cols; j++)
        {
            bool hasNonZero = false;
            for (int i = 0; i < rows; i++)
            {
                if (originalGrid[i, j] != 0)
                {
                    hasNonZero = true;
                    break;
                }
            }
            if (hasNonZero)
                validCols.Add(j);
        }
    
        // Nếu không có hàng hoặc cột hợp lệ nào
        if (validRows.Count == 0 || validCols.Count == 0)
            return new int[0, 0];
    
        // Tạo grid mới với các hàng và cột hợp lệ
        int[,] resultGrid = new int[validRows.Count, validCols.Count];
    
        for (int i = 0; i < validRows.Count; i++)
        {
            for (int j = 0; j < validCols.Count; j++)
            {
                resultGrid[i, j] = originalGrid[validRows[i], validCols[j]];
            }
        }
    
        return resultGrid;
    }
}

// Extension method để hỗ trợ timeout
public static class TaskExtensions
{
    public static async Task<T> WaitForAsync<T>(this Task<T> task, TimeSpan timeout)
    {
        if (task == await Task.WhenAny(task, Task.Delay(timeout)))
            return await task;
        else
            throw new TimeoutException();
    }
}