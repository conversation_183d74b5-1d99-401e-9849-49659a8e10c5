using System;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using b100SDK.Scripts.Utilities;

public class HamiltonianPathGeneratorTest : MonoBehaviour
{
    [Header("Test Settings")]
    [SerializeField] private int testM = 5;
    [SerializeField] private int testN = 5;
    [SerializeField] private double testFillRatio = 0.8;
    [SerializeField] private int timeoutMs = 2000; // 2 seconds timeout
    
    private HamiltonianPathGenerator generator;
    
    void Start()
    {
        generator = new HamiltonianPathGenerator();
        
        // Test timeout functionality
        TestTimeoutFunctionality();
    }
    
    private async void TestTimeoutFunctionality()
    {
        BhDebug.Log("=== Testing Hamiltonian Path Generator Timeout ===");
        
        // Test 1: Normal generation (should complete)
        BhDebug.Log("Test 1: Normal generation with reasonable timeout");
        var startTime = DateTime.Now;
        var result1 = await generator.GenerateHamiltonianPathWithTimeoutAsync(3, 3, 0.5, 5000);
        var duration1 = (DateTime.Now - startTime).TotalMilliseconds;
        
        if (result1.HasValue)
        {
            BhDebug.Log($"✓ Test 1 PASSED: Generated path in {duration1:F0}ms");
            BhDebug.Log($"  Grid size: {result1.Value.grid.GetLength(0)}x{result1.Value.grid.GetLength(1)}");
            BhDebug.Log($"  Path length: {result1.Value.path.Count}");
        }
        else
        {
            BhDebug.LogError($"✗ Test 1 FAILED: No result after {duration1:F0}ms");
        }
        
        // Test 2: Timeout scenario (should timeout)
        BhDebug.Log("\nTest 2: Large grid with short timeout (should timeout)");
        startTime = DateTime.Now;
        var result2 = await generator.GenerateHamiltonianPathWithTimeoutAsync(10, 10, 0.9, 100); // Very short timeout
        var duration2 = (DateTime.Now - startTime).TotalMilliseconds;
        
        if (result2.HasValue)
        {
            BhDebug.LogWarning($"? Test 2 UNEXPECTED: Generated path in {duration2:F0}ms (expected timeout)");
        }
        else
        {
            BhDebug.Log($"✓ Test 2 PASSED: Correctly timed out after {duration2:F0}ms");
        }
        
        // Test 3: Manual cancellation
        BhDebug.Log("\nTest 3: Manual cancellation test");
        await TestManualCancellation();
        
        BhDebug.Log("\n=== All tests completed ===");
    }
    
    private async Task TestManualCancellation()
    {
        using var cts = new CancellationTokenSource();
        
        // Start generation task
        var generationTask = generator.GenerateHamiltonianPathAsync(8, 8, 0.8, cts.Token);
        
        // Cancel after 500ms
        _ = Task.Delay(500).ContinueWith(_ => {
            BhDebug.Log("Cancelling generation task...");
            cts.Cancel();
        });
        
        var startTime = DateTime.Now;
        try
        {
            var result = await generationTask;
            var duration = (DateTime.Now - startTime).TotalMilliseconds;
            BhDebug.LogWarning($"? Test 3 UNEXPECTED: Task completed in {duration:F0}ms (expected cancellation)");
        }
        catch (OperationCanceledException)
        {
            var duration = (DateTime.Now - startTime).TotalMilliseconds;
            BhDebug.Log($"✓ Test 3 PASSED: Task correctly cancelled after {duration:F0}ms");
        }
        catch (Exception ex)
        {
            BhDebug.LogError($"✗ Test 3 FAILED: Unexpected exception: {ex.Message}");
        }
    }
    
    [ContextMenu("Run Timeout Test")]
    public void RunTimeoutTest()
    {
        TestTimeoutFunctionality();
    }
    
    [ContextMenu("Test Performance")]
    public async void TestPerformance()
    {
        BhDebug.Log("=== Performance Test ===");
        
        var generator = new HamiltonianPathGenerator();
        var iterations = 5;
        var totalTime = 0.0;
        var successCount = 0;
        
        for (int i = 0; i < iterations; i++)
        {
            var startTime = DateTime.Now;
            var result = await generator.GenerateHamiltonianPathWithTimeoutAsync(testM, testN, testFillRatio, timeoutMs);
            var duration = (DateTime.Now - startTime).TotalMilliseconds;
            
            totalTime += duration;
            
            if (result.HasValue)
            {
                successCount++;
                BhDebug.Log($"Iteration {i + 1}: Success in {duration:F0}ms, Path length: {result.Value.path.Count}");
            }
            else
            {
                BhDebug.LogWarning($"Iteration {i + 1}: Timeout after {duration:F0}ms");
            }
        }
        
        var avgTime = totalTime / iterations;
        var successRate = (successCount * 100.0) / iterations;
        
        BhDebug.Log($"=== Performance Results ===");
        BhDebug.Log($"Average time: {avgTime:F0}ms");
        BhDebug.Log($"Success rate: {successRate:F1}%");
        BhDebug.Log($"Successful generations: {successCount}/{iterations}");
    }
}
