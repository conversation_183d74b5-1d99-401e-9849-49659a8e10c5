using System;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using b100SDK.Scripts.Utilities;

public class HamiltonianPathGeneratorTest : MonoBehaviour
{
    [Header("Test Settings")]
    [SerializeField] private int testM = 5;
    [SerializeField] private int testN = 5;
    [SerializeField] private double testFillRatio = 0.8;
    [SerializeField] private int timeoutMs = 2000; // 2 seconds timeout
    
    private HamiltonianPathGenerator generator;
    
    void Start()
    {
        generator = new HamiltonianPathGenerator();

        // Test simple case first
        TestSimpleCase();
    }

    [ContextMenu("Test Simple Case")]
    public async void TestSimpleCase()
    {
        BhDebug.Log("=== Testing Simple Case to Check Attempt Count ===");

        var generator = new HamiltonianPathGenerator();

        // Test with a case that should require multiple attempts
        BhDebug.Log("Testing 4x4 grid with 0.8 fill ratio...");
        var result = await generator.GenerateHamiltonianPathWithTimeoutAsync(4, 4, 0.8, 10000);

        if (result.HasValue)
        {
            BhDebug.Log($"Result: Grid {result.Value.grid.GetLength(0)}x{result.Value.grid.GetLength(1)}, Path length: {result.Value.path.Count}");
        }
        else
        {
            BhDebug.LogError("Failed to generate path");
        }
    }
    
    private async void TestTimeoutFunctionality()
    {
        BhDebug.Log("=== Testing Hamiltonian Path Generator Timeout ===");
        
        // Test 1: Normal generation (should complete)
        BhDebug.Log("Test 1: Normal generation with reasonable timeout");
        var startTime = DateTime.Now;
        var result1 = await generator.GenerateHamiltonianPathWithTimeoutAsync(3, 3, 0.5, 5000);
        var duration1 = (DateTime.Now - startTime).TotalMilliseconds;
        
        if (result1.HasValue)
        {
            BhDebug.Log($"✓ Test 1 PASSED: Generated path in {duration1:F0}ms");
            BhDebug.Log($"  Grid size: {result1.Value.grid.GetLength(0)}x{result1.Value.grid.GetLength(1)}");
            BhDebug.Log($"  Path length: {result1.Value.path.Count}");
        }
        else
        {
            BhDebug.LogError($"✗ Test 1 FAILED: No result after {duration1:F0}ms");
        }
        
        // Test 2: Timeout scenario (should timeout)
        BhDebug.Log("\nTest 2: Large grid with short timeout (should timeout)");
        startTime = DateTime.Now;
        var result2 = await generator.GenerateHamiltonianPathWithTimeoutAsync(10, 10, 0.9, 100); // Very short timeout
        var duration2 = (DateTime.Now - startTime).TotalMilliseconds;
        
        if (result2.HasValue)
        {
            BhDebug.LogWarning($"? Test 2 UNEXPECTED: Generated path in {duration2:F0}ms (expected timeout)");
        }
        else
        {
            BhDebug.Log($"✓ Test 2 PASSED: Correctly timed out after {duration2:F0}ms");
        }
        
        // Test 3: Manual cancellation
        BhDebug.Log("\nTest 3: Manual cancellation test");
        await TestManualCancellation();
        
        BhDebug.Log("\n=== All tests completed ===");
    }
    
    private async Task TestManualCancellation()
    {
        using var cts = new CancellationTokenSource();
        
        // Start generation task
        var generationTask = generator.GenerateHamiltonianPathAsync(8, 8, 0.8, cts.Token);
        
        // Cancel after 500ms
        _ = Task.Delay(500).ContinueWith(_ => {
            BhDebug.Log("Cancelling generation task...");
            cts.Cancel();
        });
        
        var startTime = DateTime.Now;
        try
        {
            var result = await generationTask;
            var duration = (DateTime.Now - startTime).TotalMilliseconds;
            BhDebug.LogWarning($"? Test 3 UNEXPECTED: Task completed in {duration:F0}ms (expected cancellation)");
        }
        catch (OperationCanceledException)
        {
            var duration = (DateTime.Now - startTime).TotalMilliseconds;
            BhDebug.Log($"✓ Test 3 PASSED: Task correctly cancelled after {duration:F0}ms");
        }
        catch (Exception ex)
        {
            BhDebug.LogError($"✗ Test 3 FAILED: Unexpected exception: {ex.Message}");
        }
    }
    
    [ContextMenu("Run Timeout Test")]
    public void RunTimeoutTest()
    {
        TestTimeoutFunctionality();
    }
    
    [ContextMenu("Test Performance")]
    public async void TestPerformance()
    {
        BhDebug.Log("=== Performance Test ===");

        var generator = new HamiltonianPathGenerator();
        var iterations = 5;
        var totalTime = 0.0;
        var successCount = 0;

        for (int i = 0; i < iterations; i++)
        {
            var startTime = DateTime.Now;
            var result = await generator.GenerateHamiltonianPathWithTimeoutAsync(testM, testN, testFillRatio, timeoutMs);
            var duration = (DateTime.Now - startTime).TotalMilliseconds;

            totalTime += duration;

            if (result.HasValue)
            {
                successCount++;
                BhDebug.Log($"Iteration {i + 1}: Success in {duration:F0}ms, Path length: {result.Value.path.Count}");
            }
            else
            {
                BhDebug.LogWarning($"Iteration {i + 1}: Timeout after {duration:F0}ms");
            }
        }

        var avgTime = totalTime / iterations;
        var successRate = (successCount * 100.0) / iterations;

        BhDebug.Log($"=== Performance Results ===");
        BhDebug.Log($"Average time: {avgTime:F0}ms");
        BhDebug.Log($"Success rate: {successRate:F1}%");
        BhDebug.Log($"Successful generations: {successCount}/{iterations}");
    }

    [ContextMenu("Test Difficult Cases")]
    public async void TestDifficultCases()
    {
        BhDebug.Log("=== Testing Difficult Cases ===");

        var generator = new HamiltonianPathGenerator();

        // Test case 1: Large grid with high fill ratio (should require multiple attempts)
        BhDebug.Log("\n--- Test Case 1: Large grid (7x7) with high fill ratio (0.9) ---");
        var result1 = await generator.GenerateHamiltonianPathWithTimeoutAsync(7, 7, 0.9, 10000);

        // Test case 2: Medium grid with very high fill ratio
        BhDebug.Log("\n--- Test Case 2: Medium grid (5x5) with very high fill ratio (0.95) ---");
        var result2 = await generator.GenerateHamiltonianPathWithTimeoutAsync(5, 5, 0.95, 10000);

        // Test case 3: Odd dimensions that might be harder
        BhDebug.Log("\n--- Test Case 3: Odd dimensions (6x7) with high fill ratio (0.85) ---");
        var result3 = await generator.GenerateHamiltonianPathWithTimeoutAsync(6, 7, 0.85, 10000);

        // Test case 4: Multiple runs of same difficult case
        BhDebug.Log("\n--- Test Case 4: Multiple runs of difficult case (5x5, 0.9) ---");
        for (int i = 0; i < 3; i++)
        {
            BhDebug.Log($"\n  Run {i + 1}:");
            var result = await generator.GenerateHamiltonianPathWithTimeoutAsync(5, 5, 0.9, 5000);
            if (result.HasValue)
            {
                BhDebug.Log($"  ✓ Success: Path length = {result.Value.path.Count}");
            }
            else
            {
                BhDebug.LogWarning($"  ✗ Failed/Timeout");
            }
        }

        BhDebug.Log("\n=== Difficult Cases Test Completed ===");
    }
}
