using System.IO;
using System.Text;
using System.Threading.Tasks;
using AllGame.Common.Scripts;
using b100SDK.Scripts.Utilities;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using UnityEngine;

namespace AllGame.FillLink.Scripts.Data
{
    public class LevelCreator : MonoBehaviour
    {
        [SerializeField]
        private int levelPerFile = 100;
        
        [SerializeField]
        [FolderPath]
        private string folderPath;
        
        [SerializeField]
        private string fileName = "Level";
        
        [SerializeField]
        private string password = "b100_14042001";

        [SerializeField]
        private int levelCount = 1000;
        
        [SerializeField]
        private int minCellInLevel = 4;

        [SerializeField]
        private int maxCellInLevel = 100;
        
        [SerializeField]
        private Easing.TweenType tweenType = Easing.TweenType.Linear;

        [SerializeField]
        [ShowIf(nameof(tweenType), Easing.TweenType.Custom)]
        private AnimationCurve animationCurve;
        
        [SerializeField]
        private Easing.TweenType tweenTypeGroup = Easing.TweenType.Linear;
        
        [ShowIf(nameof(tweenTypeGroup), Easing.TweenType.Custom)]
        [SerializeField]
        private AnimationCurve animationCurveGroup;
        
        [Button]
        async void CreateLevel()
        {
            var loop = levelCount / levelPerFile;
            var remain = levelCount % levelPerFile;

            var currentMinLevel = 4;
            var currentMaxLevel = 100;
            
            for (int i = 0; i < loop; i++)
            {
                var minLevel = Easing.Calculate(minCellInLevel, maxCellInLevel, i / (float) loop, tweenType, animationCurve);
                var maxLevel = Easing.Calculate(minCellInLevel, maxCellInLevel, (i + 1) / (float) loop, tweenType, animationCurve);
                
                currentMinLevel = Mathf.RoundToInt(minLevel);
                currentMaxLevel = Mathf.RoundToInt(maxLevel);
                
                await CreateFile(i, levelPerFile, currentMinLevel, currentMaxLevel);
                
                await Task.Delay(500);
            }
            
            if (remain > 0)
            {
                
                await CreateFile(loop, remain, currentMinLevel, currentMaxLevel);
                await Task.Delay(500);
            }
            
            BhDebug.Log("Created " + levelCount + " levels");
        }

        private async Task CreateFile(int loop, int levelQuantity, int minCell, int maxLevel)
        {
            var correctFilePath = folderPath + "/" + fileName + loop + ".txt";
            
            var sb = new StringBuilder();
            
            for (int i = 0; i < levelQuantity; i++)
            {
                var correctLevelIndex = loop * levelPerFile + i;

                var cellInLevel = Mathf.RoundToInt(
                    Easing.Calculate(minCell, maxLevel, i / (float) levelQuantity, tweenTypeGroup, animationCurveGroup));
                
                BhDebug.Log($"Level {correctLevelIndex} - {cellInLevel} cells");
                
                /*var levelData = new LevelData();
                var correctLevel = await levelData.GetLevelAsync(correctLevelIndex);*/
                
                var correctLevel = new LevelData(correctLevelIndex);
                
                var levelString = JsonConvert.SerializeObject(correctLevel);
                
                var levelEncrypted = BalancedCrypto.Encrypt(levelString, password);
                
                sb.AppendLine(levelEncrypted);

                await Task.Delay(100);
            }
            
            File.WriteAllText(correctFilePath, sb.ToString());
            
            BhDebug.Log($"Created file {correctFilePath}");
        }
    }
}