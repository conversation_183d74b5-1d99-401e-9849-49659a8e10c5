using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using b100SDK.Scripts.Utilities;
using Newtonsoft.Json;
using UnityEngine;

namespace AllGame.FillLink.Scripts.Data
{
    public class LevelData
    {
        private int[][] _boardGame;
        
        private List<(int i, int j)> _path;
        
        [JsonProperty]
        private List<int> _pathIndex;

        private bool _hasStartIndex;
        private (int i, int j) _startIndex;
        
        private int _colorIndex;
        
        [JsonProperty]
        private int _row;
        
        [JsonProperty]
        private int _col;

        public LevelData()
        {
            
        }

        public LevelData(int level)
        {
            CreateLevel(level);
        }
        
        public async Task<LevelData> GetLevelAsync(int level)
        {
            await CreateLevelAsync(level);
            return this;
        }
        
        public int[][] GetBoardGame()
        {
            if (_boardGame == null)
            {
                _boardGame = new int[_row][];
                
                for (int i = 0; i < _row; i++)
                {
                    _boardGame[i] = new int[_col];
                }
                
                for (int i = 0; i < _row; i++)
                {
                    for (int j = 0; j < _col; j++)
                    {
                        _boardGame[i][j] = 0;
                    }
                }
                
                for (int i = 0; i < _pathIndex.Count; i++)
                {
                    var index = _pathIndex[i];
                    
                    var row = index / _col;
                    var col = index % _col;
                    
                    BhDebug.Log($"row={row} - col={col}");
                    
                    _boardGame[row][col] = 1;
                }
            }
            
            return _boardGame;
        }
        
        public int GetRow()
        {
            return _row;
        }
        
        public int GetCol()
        {
            return _col;
        }
        
        public List<(int i, int j)> GetPath()
        {
            if (_path == null)
            {
                _path = new List<(int i, int j)>();
                
                for (int i = 0; i < _pathIndex.Count; i++)
                {
                    var index = _pathIndex[i];
                    var row = index / _col;
                    var col = index % _col;
                    _path.Add((row, col));
                }
            }
            return _path;
        }
        
        public (int i, int j) GetStartIndex()
        {
            if (!_hasStartIndex)
            {
                _startIndex = GetPath()[0];
                _hasStartIndex = true;
            }
            
            return _startIndex;
        }
        
        public int GetColorIndex()
        {
            return _colorIndex;
        }


        void CreateLevel(int level)
        {
            var cellCount = GetCellCountByLevel(level);
            
            var matrixCalculator = new MatrixCalculator();
            var options = matrixCalculator.GetMultipleOptions(cellCount, 3);
            
            var randomOption = options[Random.Range(0, options.Count)];
            
            var hamiltonianPathGenerator = new HamiltonianPathGenerator();

            var generateResult =
                hamiltonianPathGenerator.GenerateHamiltonianPath(randomOption.m, randomOption.n,
                    randomOption.fillRatio);

            _boardGame = ToJaggerArray(hamiltonianPathGenerator.GetCorrectGrid(generateResult.grid));
            //_boardGame = ToJaggerArray(generateResult.grid);
            var generatePath= generateResult.path;
            
            _path = new List<(int i, int j)>();
            for (int i = 0; i < generatePath.Count; i++)
            {
                _path.Add((generatePath[i].row, generatePath[i].col - 1));
            }
            
            BhDebug.Log($"Path: {string.Join(",", _path.Select(p => $"({p.i},{p.j})"))}");
            
            _pathIndex = new List<int>();
            
            for (int i = 0; i < _path.Count; i++)
            {
                _pathIndex.Add(_path[i].i * _col + _path[i].j);
            }
            
            BhDebug.Log($"Path index: {string.Join(",", _pathIndex)}");
            
            _startIndex = _path[0];
            _hasStartIndex = true;
        }
        
        async Task CreateLevelAsync(int level)
        {
            var cellCount = GetCellCountByLevel(level);
            
            var matrixCalculator = new MatrixCalculator();
            var options = matrixCalculator.GetMultipleOptions(cellCount, 3);
            
            var randomOption = options[Random.Range(0, options.Count)];
            
            var hamiltonianPathGenerator = new HamiltonianPathGenerator();

            var count = 0;

            /*var generateResult = await 
                hamiltonianPathGenerator.GenerateHamiltonianPathAsync(randomOption.m, randomOption.n,
                    randomOption.fillRatio); */         
            
            var generateResult = await 
                hamiltonianPathGenerator.GenerateHamiltonianPathWithTimeoutAsync(randomOption.m, randomOption.n,
                    randomOption.fillRatio, 20000);
            
            while (generateResult == null && count < 1000)
            {
                count++;
                
                BhDebug.LogWarning($"Tạo path thất bại, thử lại lần {count}");
                
                generateResult = await 
                    hamiltonianPathGenerator.GenerateHamiltonianPathWithTimeoutAsync(randomOption.m, randomOption.n,
                        randomOption.fillRatio, 20000);
            }
            
            if (generateResult == null)
            {
                BhDebug.LogError("Tạo path thất bại");
                return;
            }

            _boardGame = ToJaggerArray(hamiltonianPathGenerator.GetCorrectGrid(generateResult.Value.grid));
            //_boardGame = ToJaggerArray(generateResult.grid);
            _path = generateResult.Value.path;
            
            _pathIndex = new List<int>();
            
            for (int i = 0; i < _path.Count; i++)
            {
                _pathIndex.Add(_path[i].i * _col + _path[i].j);
            }
            
            _startIndex = _path[0];
            _hasStartIndex = true;
        }

        int GetCellCountByLevel(int level)
        {
            return Mathf.Clamp(level, 4, int.MaxValue);
        }

        int[][] ToJaggerArray(int[,] array)
        {
            int rows = array.GetLength(0);
            _row = rows;
            int cols = array.GetLength(1);
            _col = cols;
            int[][] jaggedArray = new int[rows][];

            for (int i = 0; i < rows; i++)
            {
                jaggedArray[i] = new int[cols];
                for (int j = 0; j < cols; j++)
                {
                    jaggedArray[i][j] = array[i, j];
                }
            }

            return jaggedArray;
        }

        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.AppendLine( $"LevelData: {_row}x{_col} - {GetPath().Count} cells in path");
            sb.AppendLine($"Start index: {_startIndex.i},{_startIndex.j}");
            sb.AppendLine($"Path:");
            for (int i = 0; i < _path.Count; i++)
            {
                sb.Append($"({_path[i].i},{_path[i].j})");
                if (i < _path.Count - 1) sb.Append(" -> ");
            }
            sb.AppendLine();
            sb.AppendLine($"Color index: {_colorIndex}");
            sb.AppendLine($"Board game:");
            for (int i = 0; i < _row; i++)
            {
                for (int j = 0; j < _col; j++)
                {
                    sb.Append(GetBoardGame()[i][j] + " ");
                }
                sb.AppendLine();
            }
            return sb.ToString();
        }
    }
}