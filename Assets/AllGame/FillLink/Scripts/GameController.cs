using AllGame.FillLink.Scripts.Data;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.DesignPatterns;
using Sirenix.OdinInspector;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace AllGame.FillLink.Scripts
{
    public class GameController : <PERSON>h<PERSON><PERSON><PERSON><GameController>
    {
#if UNITY_EDITOR
        private bool CanShowData => EditorApplication.isPlaying;
        [ShowIf(nameof(CanShowData))]
        [SerializeField]
#endif
        private FillLinkData _data;
        
        
        protected override void Awake()
        {
            base.Awake();
            
            LoadData();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            SaveData();
        }

        public FillLinkData GetData() => _data;
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                SaveData();
            }
        }

        private void LoadData()
        {
            _data = Database.LoadData<FillLinkData>();
        }

        private void SaveData()
        {
            Database.SaveData(_data);
        }
    }
}