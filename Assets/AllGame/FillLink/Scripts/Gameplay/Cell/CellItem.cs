using AllGame.FillLink.Scripts.Gameplay.Line;
using b100SDK.Scripts.Utilities;
using UnityEngine;
using UnityEngine.UI;

namespace AllGame.FillLink.Scripts.Gameplay.Cell
{
    public class CellItem : MonoBehaviour
    {
        [SerializeField]
        private RectTransform rectTransform;

        [SerializeField]
        private Image image;

        private bool _isInLine;
        private LinePiece _inPiece;
        private LinePiece _outPiece;

        public (int i, int j) Index { get; private set; }
        public LinePiece InPiece => _inPiece;
        public LinePiece OutPiece => _outPiece;
        
        public RectTransform RectTransform => rectTransform;

        public void Init((int i, int j) index, Vector2 cellSize, bool isInLine)
        {
            _isInLine = isInLine;
            Index = index;
            UpdateCellSize(cellSize);
            UpdateVisual();
            
            _inPiece = null;
            _outPiece = null;

        }

        private void UpdateCellSize(Vector2 cellSize)
        {
            rectTransform.sizeDelta = cellSize;
        }

        public void SetInPiece(LinePiece inPiece)
        {
            _inPiece = inPiece;
        }

        public void SetOutPiece(LinePiece outPiece)
        {
            _outPiece = outPiece;
        }

        void UpdateVisual()
        {
            image.gameObject.SetActiveWithChecker(_isInLine);
        }


        public bool IsNearCenter(Vector3 position)
        {
            var distance = Vector2.Distance(position, rectTransform.position);
            return distance < rectTransform.sizeDelta.x * 0.1f;
        }
    }
}