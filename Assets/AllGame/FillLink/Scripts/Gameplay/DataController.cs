using System.Collections.Generic;
using System.Linq;
using AllGame.Common.Scripts;
using AllGame.FillLink.Scripts.Data;
using b100SDK.Scripts.Asset;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Tool;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using UnityEngine;

namespace AllGame.FillLink.Scripts.Gameplay
{
    public class DataController : MonoBehaviour
    {
        [SerializeField]
        private SerializedDictionary<int, string> levelPathMap;
        
        [SerializeField]
        private string password = "GamePass123!";
        
        private string _currentFileString;
        private string _currentLevelString;


        public LevelData GetLevel(int level)
        {
            var allKeys = levelPathMap.Keys.ToList();
            allKeys.Sort();

            var correctStartLevel = allKeys.LastOrDefault(x => level >= x);

            if (correctStartLevel < 0)
            {
                correctStartLevel = allKeys.Max();
            }
            
            BhDebug.Log("Correct start level: " + correctStartLevel);
            
            _currentFileString = AssetManager.LoadAsset<TextAsset>(levelPathMap[correctStartLevel]).text;
                
            var allLevel = _currentFileString.Split("\n").ToList();
            _currentLevelString = allLevel[level - correctStartLevel];
            
            var correctLevelDecrypt = BalancedCrypto.Decrypt(_currentLevelString, password);
            
            BhDebug.Log("Correct level decrypt: " + correctLevelDecrypt);
            
            return JsonConvert.DeserializeObject<LevelData>(correctLevelDecrypt);
        }




#if UNITY_EDITOR
        
        [SerializeField]
        string levelPath;
        
        [Button]
        void GetAllLevel()
        {
            var allTextAsset = UtilitiesTool.GetResources<TextAsset>(levelPath, new List<string>() { ".json" , ".txt" });
            
            allTextAsset.Sort((a,b) => CompareUtilities.NaturalCompare(a.name, b.name));

            levelPathMap.Clear();
            
            foreach (var textAsset in allTextAsset)
            {
                var level = int.Parse(textAsset.name.Replace("Level_", ""));
                levelPathMap.TryAdd(100 * level, UtilitiesTool.GetResourcePath(textAsset));
            }
        }
#endif
    }
}