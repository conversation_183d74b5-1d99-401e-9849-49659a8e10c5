using AllGame.FillLink.Scripts.Gameplay.Line;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.ObjectPooling;
using UnityEngine;
using SpawnPool = PathologicalGames.SpawnPool;

namespace AllGame.FillLink.Scripts.Gameplay
{
    public class FillLinkPoolManager : Bh<PERSON>ingleton<FillLinkPoolManager>
    {
        [SerializeField]
        private SpawnPool linePiecePool;
        
        [SerializeField]
        private LinePiece linePiecePrefab;
        
        
        
        public LinePiece GetLinePiece()
        {
            return linePiecePool.Spawn(linePiecePrefab.transform).GetComponent<LinePiece>();
        }
        
        public void Despawn(LinePiece linePiece)
        {
            linePiecePool.Despawn(linePiece.transform);
        }
        
        public void DespawnAllLinePiece()
        {
            linePiecePool.DespawnAll();
        }
        
    }
}