using System;
using AllGame.FillLink.Scripts.Data;
using AllGame.FillLink.Scripts.Gameplay.Cell;
using AllGame.FillLink.Scripts.Gameplay.Line;
using Sirenix.OdinInspector;
using UnityEngine;

namespace AllGame.FillLink.Scripts.Gameplay
{
    public class GameplayController : MonoBehaviour
    {
        [SerializeField]
        private BoardController boardController;

        [SerializeField]
        private LineController lineController;

        [SerializeField]
        private DataController dataController;
        
        private LevelData _levelData;


        private void Start()
        {
            InitLevel();
        }

        [Button]
        public void InitLevel()
        {
            _levelData = new LevelData(GameController.Instance.GetData().level);
            
            //_levelData = dataController.GetLevel(GameController.Instance.GetData().level);
            
            boardController.Init(_levelData);
            lineController.Init(boardController.GetCellSize());
        }
        
        [Button]
        void NextLevel() {
            GameController.Instance.GetData().level++;
            InitLevel();
        }
    }
}