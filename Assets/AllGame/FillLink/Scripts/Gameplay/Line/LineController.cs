using System.Collections.Generic;
using AllGame.FillLink.Scripts.Gameplay.Cell;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using TMPro;
using UnityEngine;

namespace AllGame.FillLink.Scripts.Gameplay.Line
{
    public class LineController : MonoBehaviour
    {
        [SerializeField]
        private BoardController boardController;

        [SerializeField]
        private TMP_Text dotText;
        
        private List<CellItem> _cellItems = new();
        private List<LinePiece> _linePieces = new();

        private LinePiece _currentPieceInteractive;
        public LinePiece CurrentPieceInteractive => _currentPieceInteractive;

        private Vector2 _pieceSize;

        public void Init(Vector2 pieceSize)
        {
            _pieceSize = pieceSize;
            Clear();
        }

        public void StartDraw(CellItem startCell)
        {
            Clear(); // Clear previous line
            AddCell(startCell);
        }

        public void AddCellToCurrentLine(CellItem cellItem)
        {
            // Check if this cell is already in the current line
            if (_cellItems.Contains(cellItem))
            {
                BhDebug.Log("Cell already in line");
                return;
            }
            
            // Check if this cell is adjacent to the last cell (optional validation)
            if (_cellItems.Count > 0)
            {
                var lastCell = _cellItems[_cellItems.Count - 1];
                if (!IsAdjacent(lastCell, cellItem))
                {
                    // If not adjacent, you can choose to ignore or create a direct line
                    // For now, we'll allow any connection
                    BhDebug.Log("Cell not adjacent, but added anyway");
                    return;
                }
            }
            
            AddCell(cellItem);
        }
        
        private void AddCell(CellItem cellItem)
        {
            if (_currentPieceInteractive)
            {
                _currentPieceInteractive.SetEndCell(cellItem);
                cellItem.SetInPiece(_currentPieceInteractive);
            }
            
            _cellItems.Add(cellItem);
            BhDebug.Log("Add cell: " + cellItem.name);
            
            _currentPieceInteractive = SpawnPiece(cellItem);
            _linePieces.Add(_currentPieceInteractive);
            
            _currentPieceInteractive.SetEndCell(null);
            cellItem.SetOutPiece(_currentPieceInteractive);
        }

        LinePiece SpawnPiece(CellItem startCell)
        {
            var newPiece = FillLinkPoolManager.Instance.GetLinePiece();
            newPiece.transform.SetParent(transform);
            newPiece.Init(boardController, this, startCell, _pieceSize.x, _pieceSize.y);
            return newPiece;
        }
        
        public bool IsAdjacent(CellItem cell1, CellItem cell2)
        {
            var index1 = cell1.Index;
            var index2 = cell2.Index;
            
            int deltaI = Mathf.Abs(index1.i - index2.i);
            int deltaJ = Mathf.Abs(index1.j - index2.j);
            
            // Adjacent means either same row/column and distance 1, or diagonal
            return (deltaI == 1 && deltaJ == 0) || (deltaI == 0 && deltaJ == 1);
        }
        
        
        public void RemoveCellFromCurrentLine(CellItem cellItem)
        {
            // Check if this cell is in the current line
            if (!_cellItems.Contains(cellItem)) 
                return;
            
            // Remove the cell and update the line
            RemoveCell(cellItem);
        }

        private void RemoveCell(CellItem cellItem)
        {
            _cellItems.Remove(cellItem);
            
            if (!cellItem)
                return;
            
            cellItem.SetInPiece(null);
            cellItem.SetOutPiece(null);
            
            if (_currentPieceInteractive)
            {
                _currentPieceInteractive.SetEndCell(null);
                FillLinkPoolManager.Instance.Despawn(_currentPieceInteractive);
                _linePieces.Remove(_currentPieceInteractive);
            }

            if (_linePieces.Count == 0)
            {
                _currentPieceInteractive = null;
                return;
            }

            _currentPieceInteractive = _linePieces.GetLast();
            _currentPieceInteractive.SetEndCell(null);
        }

        void Clear()
        {
            _currentPieceInteractive = null;
            
            ClearCell();
            ClearLine();
        }

        void ClearCell()
        {
            // Clear cell references
            foreach (var cell in _cellItems)
            {
                cell.SetInPiece(null);
                cell.SetOutPiece(null);
            }
            _cellItems.Clear();
        }

        void ClearLine()
        {
            foreach (var linePiece in _linePieces)
            {
                FillLinkPoolManager.Instance.Despawn(linePiece);
            }

            _linePieces.Clear();
        }

        public void SetPoint(Vector3 position)
        {
            if (_currentPieceInteractive)
            {
                _currentPieceInteractive.SetPosition(position);
            }
        }

        public void ConfirmCurrentLine()
        {
            if (_currentPieceInteractive)
            {
                // Hide the interactive piece since line is confirmed
                _currentPieceInteractive.gameObject.SetActive(false);
            }
            
            // TODO: Add your line confirmation logic here
            //Debug.Log($"Line confirmed with {_cellItems.Count} cells");
            
            // Optionally clear the line after confirmation
            // Clear();
        }

        public bool NeedAddCell(CellItem cellItem, Vector3 position)
        {
            if (!_currentPieceInteractive) 
                return false;
            
            var startCellOfCurrentPiece = _currentPieceInteractive.GetStartCell();

            var dirFromPointToLastPoint = position - cellItem.transform.position;
            var dirFromPointToStartCell = position - startCellOfCurrentPiece.transform.position;
            var dotValue = Vector3.Dot(dirFromPointToLastPoint, dirFromPointToStartCell);
            
            dotText.text  = dotValue.ToString();
            dotText.color = dotValue < 0 ? Color.red : Color.green;
            
            return Vector3.Dot(dirFromPointToLastPoint, dirFromPointToStartCell) > 0;
        }
        
        public bool NeedRemoveCell(CellItem cellItem, Vector3 position)
        {
            if (!_currentPieceInteractive) 
                return false;
            
            var startCellOfCurrentPiece = _currentPieceInteractive.GetStartCell();

            var dirFromPointToLastPoint = position - cellItem.transform.position;
            if (_cellItems.Count < 2) 
                return false;
            var dirFromPointToPreviousPoint = position - _cellItems[_cellItems.Count - 2].transform.position;
            var dir = cellItem.transform.position - startCellOfCurrentPiece.transform.position;
            
            var dotValue = Vector3.Dot(dirFromPointToLastPoint, dirFromPointToPreviousPoint);
            dotText.text  = dotValue.ToString();
            dotText.color = dotValue < 0 ? Color.red : Color.green;
            

            return Vector3.Dot(dirFromPointToLastPoint, dirFromPointToPreviousPoint) < 0;
        }

        public CellItem GetLastCell()
        {
            if (_cellItems.Count <= 0) 
                return null;
            
            return _cellItems.GetLast();
        }
        
        public bool IsCellInLine(CellItem cellItem)
        {
            return _cellItems.Contains(cellItem);
        }

        public void ClearCells(CellItem hoveredCell, CellItem lastCell)
        {
            while (lastCell != hoveredCell && lastCell && IsCellInLine(lastCell))
            {
                RemoveCellFromCurrentLine(lastCell);
                lastCell = GetLastCell();
            }
        }
    }
}