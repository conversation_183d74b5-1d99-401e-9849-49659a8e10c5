using AllGame.FillLink.Scripts.Gameplay.Cell;
using UnityEngine;
using UnityEngine.UI;

namespace AllGame.FillLink.Scripts.Gameplay.Line
{
    public class LinePiece : MonoBehaviour
    {
        [SerializeField]
        private Image lineImage;


        private BoardController _boardController;
        private LineController _lineController;
        
        private float _lineWidth = 10f;
        private float _lineHeigh = 10f;
        
        private CellItem _startCell;
        private CellItem _endCell;
        private RectTransform _rectTransform;
        private Canvas _canvas;
        
        private void Awake()
        {
            _rectTransform = GetComponent<RectTransform>();
            _canvas = FindFirstObjectByType<Canvas>();
        }
        
        public void Init(BoardController boardController, LineController lineController, CellItem startCell, float lineWidth, float maxLineHeight)
        {
            _boardController = boardController;
            _lineController = lineController;
            
            _startCell = startCell;
            _lineWidth = lineWidth * .6f;
            _lineHeigh = maxLineHeight;
            _endCell = null;
            
            // Set initial position to start cell center
            _rectTransform.anchoredPosition = GetCellCenterPosition(_startCell);
            
            // Initialize line appearance
            lineImage.color = Color.white; // You can customize this
            SetLineVisibility(false);
        }
        
        public void SetPosition(Vector3 screenPosition)
        {
            if (!_startCell) 
                return;
            
            // Convert screen position to canvas local position
            Vector2 localPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                _canvas.transform as RectTransform, 
                screenPosition, 
                _canvas.worldCamera, 
                out localPoint);
            
            Vector2 startPos = GetCellCenterPosition(_startCell);
            
            // Calculate line from start cell center to current position
            UpdateLine(startPos, localPoint);
        }
        
        public void SetEndCell(CellItem endCell)
        {
            _endCell = endCell;
            
            if (_endCell && _startCell)
            {
                // Snap line to connect cell centers
                Vector2 startPos = GetCellCenterPosition(_startCell);
                Vector2 endPos = GetCellCenterPosition(_endCell);
                UpdateLine(startPos, endPos);
            }
        }
        
        private Vector2 GetCellCenterPosition(CellItem cell)
        {
            if (!cell) 
                return Vector2.zero;
            
            return cell.RectTransform.anchoredPosition;
        }
        
        
        bool CanDraw(Vector2 startPos, Vector2 endPos)
        {
            var endCell = _boardController.GetCellAtPosition(endPos);

            if (!endCell || !endCell.gameObject.activeInHierarchy)
                return false;

            if (_lineController.IsCellInLine(endCell))
                return false;
            
            var lastCellInLine = _lineController.GetLastCell();
            
            if (!_lineController.IsAdjacent(lastCellInLine, endCell))
                return false;

            return true;
        }
        
        private void UpdateLine(Vector2 startPos, Vector2 endPos)
        {
            // Calculate line properties
            Vector2 direction = endPos - startPos;
            float distance = direction.magnitude;
            
            if (distance < 0.1f)
            {
                SetLineVisibility(false);
                return;
            }
            
            if (!CanDraw(startPos, endPos))
            {
                SetLineVisibility(false);
                return;
            }
            
            
            // Only allow horizontal or vertical lines
            Vector2 adjustedEndPos;
            float finalDistance;
            float angle;
            
            if (Mathf.Abs(direction.x) > Mathf.Abs(direction.y))
            {
                // Horizontal line
                adjustedEndPos = new Vector2(endPos.x, startPos.y);
                finalDistance = Mathf.Abs(direction.x);
                angle = direction.x > 0 ? 0f : 180f;
            }
            else
            {
                // Vertical line
                adjustedEndPos = new Vector2(startPos.x, endPos.y);
                finalDistance = Mathf.Abs(direction.y);
                angle = direction.y > 0 ? 90f : -90f;
            }
            
            if (finalDistance < 0.1f)
            {
                SetLineVisibility(false);
                return;
            }
            
            SetLineVisibility(true);
            
            // Set line position (center point between start and adjusted end)
            Vector2 centerPos = (startPos + adjustedEndPos) * 0.5f;
            _rectTransform.anchoredPosition = centerPos;
            
            // Set line size (length and width)
            _rectTransform.sizeDelta = new Vector2(finalDistance, _lineWidth);
            
            // Set line rotation
            _rectTransform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
        }
        
        private void SetLineVisibility(bool visible)
        {
            lineImage.enabled = visible;
        }
        
        public CellItem GetStartCell()
        {
            return _startCell;
        }
        
        public CellItem GetEndCell()
        {
            return _endCell;
        }
        
        // Method to check if this line connects two specific cells
        public bool ConnectsCells(CellItem cell1, CellItem cell2)
        {
            return (_startCell == cell1 && _endCell == cell2) || 
                   (_startCell == cell2 && _endCell == cell1);
        }
        
        
        public Vector3 GetEndPoint()
        {
            if (_endCell != null)
            {
                // If we have an end cell, return its center position in world coordinates
                Vector2 localEndPos = GetCellCenterPosition(_endCell);
                return _canvas.transform.TransformPoint(localEndPos);
            }
            else if (_startCell != null)
            {
                // If no end cell but we have a start cell, return the current line end position
                // Get the actual size and rotation of the line
                float lineLength = _rectTransform.sizeDelta.x;
                float angle = _rectTransform.rotation.eulerAngles.z;
                
                // Calculate direction vector based on the line's current rotation
                // We need half the length because we want to go from center to end
                float halfLength = lineLength / 2f;
                
                // Convert angle to radians and calculate the direction vector
                float angleRad = angle * Mathf.Deg2Rad;
                Vector2 direction = new Vector2(
                    halfLength * Mathf.Cos(angleRad),
                    halfLength * Mathf.Sin(angleRad)
                );
                
                // The end position is the center position plus the direction vector
                Vector2 localEndPos = _rectTransform.anchoredPosition + direction;
                return _canvas.transform.TransformPoint(localEndPos);
            }
            
            // Fallback: return current transform position
            return transform.position;
        }
    }
}