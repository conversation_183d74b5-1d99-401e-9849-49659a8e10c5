using System;
using System.Linq;
using AllGame.FillLink.Scripts.Gameplay.Cell;
using AllGame.FillLink.Scripts.Gameplay.Line;
using b100SDK.Scripts.Utilities;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace AllGame.FillLink.Scripts.Gameplay
{
    public class UserInput : MonoBeh<PERSON>our, IPointerDownHandler, IPointerUpHandler, IDragHandler, IBeginDragHandler, IEndDragHandler
    {
        [SerializeField]
        private LineController lineController;

        [SerializeField]
        private BoardController boardController;

        [SerializeField]
        private GraphicRaycaster graphicRaycaster;

        private bool _isDrawing;
        private CellItem _lastHovered;
        private Vector2 _lastMousePosition; // Thêm biến lưu vị trí trước đó

        public void OnPointerDown(PointerEventData eventData)
        {
            var item = ControlUtilities.GetItemOverlapOnUI<CellItem>(graphicRaycaster, eventData.position);

            if (item)
            {
                _isDrawing = true;
                _lastHovered = item;
                _lastMousePosition = eventData.position; // L<PERSON>u vị trí ban đầu
                lineController.StartDraw(item);
            }
        }

        public void OnDrag(PointerEventData eventData)
        {
            if (!_isDrawing) return;
            
            InterpolateBetweenPositions(eventData.position);
        
            _lastMousePosition = eventData.position;
        }
        private void InterpolateBetweenPositions(Vector2 endPos)
        {
            ValidateCell(endPos);
            
            var lastCell = lineController.GetLastCell();

            while (lineController.NeedRemoveCell(lastCell, endPos))
            {
                lineController.RemoveCellFromCurrentLine(lastCell);
                lastCell = lineController.GetLastCell();
            }
            
            var startPos = lastCell.transform.position;
            
            var allCell = boardController.GetCellBetween(startPos, endPos);
        
            foreach (var cell in allCell)
            {
                CheckCellAtPosition(cell, endPos);
            }
            
            lineController.SetPoint(endPos);
        }


        void ValidateCell(Vector2 position)
        {
            var hoveredCell = ControlUtilities.GetItemOverlapOnUI<CellItem>(graphicRaycaster, position);
        
            if (!hoveredCell) 
                return;
            
            var lastCell = lineController.GetLastCell();
            
            if (!lastCell) 
                return;
            
            if (hoveredCell != lastCell && lineController.IsCellInLine(hoveredCell))
            {
                lineController.ClearCells(hoveredCell, lastCell);
            }
        }

        private void CheckCellAtPosition(CellItem cell, Vector2 position)
        {
            if (cell != _lastHovered && lineController.NeedAddCell(cell, position))
            {
                lineController.AddCellToCurrentLine(cell);
                _lastHovered = cell;
            }
        }

        public void OnPointerUp(PointerEventData eventData)
        {
            if (_isDrawing)
            {
                _isDrawing = false;
                lineController.ConfirmCurrentLine();
                _lastHovered = null;
            }
        }

        public void OnBeginDrag(PointerEventData eventData)
        {
            BhDebug.Log("Start draw");
        }

        public void OnEndDrag(PointerEventData eventData)
        {
            BhDebug.Log("End draw");
        }
    }
}