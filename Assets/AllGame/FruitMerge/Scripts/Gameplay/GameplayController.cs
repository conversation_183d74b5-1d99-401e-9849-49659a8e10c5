using System;
using System.Collections.Generic;
using AllGame.FruitMerge.Scripts.Configs;
using AllGame.FruitMerge.Scripts.Events;
using b100SDK.Scripts.Audio;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.EventHandler.Events;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Vibration;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace AllGame.FruitMerge.Scripts.Gameplay
{
    public class GameplayController : BhMonoBehavior
    {
        [Header("Config")]
        [SerializeField]
        private AssetConfig config;
        
        [Header("Bough")]
        [SerializeField]
        private BoughController boughController;

        [Header("Merge")]
        [SerializeField]
        private MergeController mergeController;
        
        [Header("Event")]
        [SerializeField]
        private VoidEventChannel loseEventChannel;
        
        [SerializeField]
        private VoidEventChannel replayEventChannel;


        private List<FruitItem> _fruitItems = new();
        
        public bool IsLose { get; set; }
        
        private void OnEnable()
        {
            loseEventChannel.OnEventRaised += Lose;
            replayEventChannel.OnEventRaised += Replay;
        }
        
        private void OnDisable()
        {
            loseEventChannel.OnEventRaised -= Lose;
            replayEventChannel.OnEventRaised -= Replay;
        }

        private void Start()
        {
            InitGame();
            SoundManager.Instance.PlayMusic();
        }

        void InitGame()
        {
            _fruitItems.Clear();
            
            IsLose = false;
            boughController.Init();
            mergeController.Init();
        }

        void Lose()
        {
            if (IsLose)
                return;
            
            IsLose = true;
            
            BhDebug.LogError("Lose");
            ShotFeedbackLose();
        }

        void Replay()
        {
            InitGame();
        }

        void ShotFeedbackLose()
        {
            // Shot sound
            SoundManager.Instance.PlaySoundLose();
            BhVibrate.Haptic(BhHapticTypes.Failure);
        }
        
        
        public void AddFruitItem(FruitItem fruitItem)
        {
            _fruitItems.Add(fruitItem);
        }
        
        public void RemoveFruitItem(FruitItem fruitItem)
        {
            _fruitItems.Remove(fruitItem);
        }
        
        
        public List<FruitItem> GetFruitItems() => _fruitItems;
    }
}