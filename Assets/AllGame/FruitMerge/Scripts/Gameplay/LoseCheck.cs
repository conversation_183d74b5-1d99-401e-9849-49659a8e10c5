using System.Collections;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.EventHandler.Events;
using b100SDK.Scripts.Utilities;
using UnityEngine;

namespace AllGame.FruitMerge.Scripts.Gameplay
{
    public class LoseCheck : BhMonoBehavior
    {
        [SerializeField]
        private BoxCollider2D boxCollider;
        
        [SerializeField]
        private float loseCheckDuration = 3f;
        
        [SerializeField]
        private VoidEventChannel onLose;
        
        
        private bool _hasFruitLose;

        private void OnTriggerEnter2D(Collider2D other)
        {
            if (other.TryGetComponent<FruitItem>(out var item))
            {
                if (item.IsCollideWithOtherFruit)
                {
                    BhDebug.Log("Fruit enter");
                    _hasFruitLose = true;
                            
                    StartCoroutineLose();
                }
            }
        }

        private void OnTriggerExit2D(Collider2D other)
        {
            if (other.TryGetComponent<FruitItem>(out var item))
            {
                BhDebug.Log("Fruit exist");
                
                CheckFruitIsInWarningRange();
                
                if (!_hasFruitLose)
                {
                    ClearLoseCoroutine();
                }
            }
        }
        

        private Collider2D[] _colliders = new Collider2D[10];
        
        void CheckFruitIsInWarningRange()
        {
            Vector2 boxCenter = (Vector2)transform.position + boxCollider.offset;
            Vector2 boxSize = boxCollider.size;
            float angle = transform.eulerAngles.z;

            var size = Physics2D.OverlapBoxNonAlloc(boxCenter, boxSize, angle, _colliders);

            if (size > 0)
            {
                for (int i = 0; i < size; i++)
                {
                    if (_colliders[i].TryGetComponent<FruitItem>(out var fruitItem))
                    {
                        _hasFruitLose = true;
                        return;
                    }
                }

                _hasFruitLose = false;
            }
            else
            {
                _hasFruitLose = false;
            }
        }


        void StartCoroutineLose()
        {
            BhDebug.Log("Call start lose");
            
            if (_loseCoroutine == null)
            {
                _loseCoroutine = StartCoroutine(LoseCheckCoroutine());
            }
            else
            {
                BhDebug.LogWarning("Coroutine started!!!");
            }
        }
        
        private void ClearLoseCoroutine()
        {
            BhDebug.Log("ClearCoroutine Lose");
            if (_loseCoroutine != null)
            {
                StopCoroutine(_loseCoroutine);
                _loseCoroutine = null;
            }
        }

        private Coroutine _loseCoroutine;

        IEnumerator LoseCheckCoroutine()
        {
            BhDebug.Log("Coroutine Lose Start");

            
            float cdLose = 0f;
            
            while (_hasFruitLose)
            {
                cdLose += Time.deltaTime;
                
                BhDebug.Log("Lose cd time: " + cdLose);

                if (cdLose >= loseCheckDuration)
                {
                    onLose?.RaiseEvent();
                    break;
                }
                
                yield return null;
            }
            
            yield return null;
        }
    }
}