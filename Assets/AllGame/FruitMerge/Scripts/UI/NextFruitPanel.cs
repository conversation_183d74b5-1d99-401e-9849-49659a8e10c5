using AllGame.FruitMerge.Scripts.Configs;
using AllGame.FruitMerge.Scripts.Events;
using AllGame.FruitMerge.Scripts.Gameplay;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace AllGame.FruitMerge.Scripts.UI
{
    public class NextFruitPanel : BhMonoBehavior
    {
        [SerializeField]
        private Image nextFruitImage;

        [SerializeField]
        private RectTransform container;
        
        [SerializeField]
        private ImageSizer nextFruitSizer;

        [SerializeField]
        private float scaleDuration = .5f;

        [SerializeField]
        private AssetConfig config;
        
        [SerializeField]
        private UpdateNextFruitEventChannel updateNextFruitEventChannelEvent;
        
        private void OnEnable()
        {
            updateNextFruitEventChannelEvent.OnEventRaised += SetNextFruit;
        }

        private void OnDisable()
        {
            updateNextFruitEventChannelEvent.OnEventRaised -= SetNextFruit;
        }
        
        void SetNextFruit(FruitType fruitType)
        {
            var icon = config.GetFruitIcon(fruitType);
            SetNextFruit(icon);
        }

        void SetNextFruit(Sprite icon)
        {
            nextFruitImage.sprite = icon;
            nextFruitSizer.FitInParent(container);

            transform.DOKill(true);
            nextFruitImage.transform.localScale = Vector3.zero;

            nextFruitImage.transform.DOScale(1f, scaleDuration).SetTarget(transform)
                .OnComplete(() =>
                {
                    nextFruitImage.transform.localScale = Vector3.one;
                });
        }
    }
}