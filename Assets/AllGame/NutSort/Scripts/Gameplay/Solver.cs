using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace AllGame.NutSort.Scripts.Gameplay
{
    [System.Serializable]
    public class WaterSortMove
    {
        public int fromTube;
        public int toTube;
        public int amount;

        public WaterSortMove(int from, int to, int amt)
        {
            fromTube = from;
            toTube = to;
            amount = amt;
        }

        public override string ToString()
        {
            return $"Move {amount} from tube {fromTube} to tube {toTube}";
        }
    }

    [System.Serializable]
    public class WaterSortSolver
    {
        [SerializeField]
        private List<List<int>> tubes;

        [SerializeField]
        private int tubeCapacity;

        private Dictionary<string, int> visitedStates; // Store minimum moves to reach this state
        private List<WaterSortMove> bestSolution;
        private int bestSolutionLength;

        public WaterSortSolver(int capacity = 4)
        {
            tubes = new List<List<int>>();
            tubeCapacity = capacity;
            visitedStates = new Dictionary<string, int>();
            bestSolution = new List<WaterSortMove>();
            bestSolutionLength = int.MaxValue;
        }

        // Khởi tạo puzzle từ mảng 2D
        public void InitializePuzzle(int[][] puzzleData)
        {
            tubes.Clear();
            foreach (var tubeData in puzzleData)
            {
                tubes.Add(new List<int>(tubeData));
            }
        }

        // Thêm ống mới
        public void AddTube(int[] colors)
        {
            tubes.Add(new List<int>(colors));
        }

        // Thêm ống rỗng
        public void AddEmptyTube()
        {
            tubes.Add(new List<int>());
        }

        // Lấy trạng thái hiện tại của puzzle
        public List<List<int>> GetCurrentState()
        {
            return tubes.Select(tube => new List<int>(tube)).ToList();
        }

        // Kiểm tra xem có thể đổ từ ống A sang ống B không
        private bool CanPour(int fromTube, int toTube)
        {
            if (fromTube == toTube) return false;
            if (tubes[fromTube].Count == 0) return false;
            if (tubes[toTube].Count >= tubeCapacity) return false;

            if (tubes[toTube].Count == 0) return true;

            return tubes[fromTube][tubes[fromTube].Count - 1] == tubes[toTube][tubes[toTube].Count - 1];
        }

        // Kiểm tra xem nước đi có hữu ích không (tránh nước đi vô nghĩa)
        private bool IsMoveUseful(int fromTube, int toTube)
        {
            if (!CanPour(fromTube, toTube)) return false;

            // Tránh đổ từ ống đã hoàn thành
            if (IsCompleted(fromTube)) return false;

            // Tránh đổ vào ống đã hoàn thành
            if (IsCompleted(toTube)) return false;

            // Tránh đổ vào ống rỗng nếu ống nguồn chỉ có 1 màu
            if (tubes[toTube].Count == 0 && HasSingleColor(fromTube))
            {
                return false;
            }

            // Ưu tiên đổ để hoàn thành ống
            if (tubes[toTube].Count > 0)
            {
                int topColor = tubes[toTube][tubes[toTube].Count - 1];
                int sameColorCount = CountTopSameColor(toTube);
                int availableSpace = tubeCapacity - tubes[toTube].Count;
                int sourceColorCount = CountTopSameColor(fromTube);

                // Nếu có thể hoàn thành ống đích
                if (sameColorCount + Mathf.Min(sourceColorCount, availableSpace) == tubeCapacity)
                {
                    return true;
                }
            }

            return true;
        }

        // Kiểm tra ống đã hoàn thành chưa
        private bool IsCompleted(int tubeIndex)
        {
            if (tubes[tubeIndex].Count != tubeCapacity) return false;
            if (tubes[tubeIndex].Count == 0) return false;

            int firstColor = tubes[tubeIndex][0];
            return tubes[tubeIndex].All(color => color == firstColor);
        }

        // Kiểm tra ống chỉ có 1 màu
        private bool HasSingleColor(int tubeIndex)
        {
            if (tubes[tubeIndex].Count == 0) return false;
            int firstColor = tubes[tubeIndex][0];
            return tubes[tubeIndex].All(color => color == firstColor);
        }

        // Đếm số lần xuất hiện liên tiếp của màu ở đỉnh
        private int CountTopSameColor(int tubeIndex)
        {
            if (tubes[tubeIndex].Count == 0) return 0;

            int topColor = tubes[tubeIndex][tubes[tubeIndex].Count - 1];
            int count = 0;

            for (int i = tubes[tubeIndex].Count - 1; i >= 0; i--)
            {
                if (tubes[tubeIndex][i] == topColor)
                    count++;
                else
                    break;
            }

            return count;
        }

        // Thực hiện đổ nước và trả về số lượng đã đổ
        private int Pour(int fromTube, int toTube)
        {
            if (!CanPour(fromTube, toTube)) return 0;

            int colorToPour = tubes[fromTube][tubes[fromTube].Count - 1];
            int amountToPoured = 0;
            int spaceAvailable = tubeCapacity - tubes[toTube].Count;

            // Đếm số lượng màu giống nhau ở đỉnh ống nguồn
            int sameColorCount = CountTopSameColor(fromTube);

            // Đổ số lượng tối đa có thể
            amountToPoured = Mathf.Min(sameColorCount, spaceAvailable);

            for (int i = 0; i < amountToPoured; i++)
            {
                int color = tubes[fromTube][tubes[fromTube].Count - 1];
                tubes[fromTube].RemoveAt(tubes[fromTube].Count - 1);
                tubes[toTube].Add(color);
            }

            return amountToPoured;
        }

        // Hoán tác nước đi
        private void UndoPour(int fromTube, int toTube, int amount)
        {
            for (int i = 0; i < amount; i++)
            {
                int color = tubes[toTube][tubes[toTube].Count - 1];
                tubes[toTube].RemoveAt(tubes[toTube].Count - 1);
                tubes[fromTube].Add(color);
            }
        }

        // Kiểm tra xem puzzle đã được giải chưa
        public bool IsSolved()
        {
            foreach (var tube in tubes)
            {
                if (tube.Count == 0) continue;
                if (tube.Count != tubeCapacity) return false;

                int firstColor = tube[0];
                foreach (int color in tube)
                {
                    if (color != firstColor) return false;
                }
            }

            return true;
        }

        // Tạo khóa trạng thái duy nhất cho trạng thái hiện tại
        private string GetStateKey()
        {
            return GetStateKey(tubes);
        }

        // Heuristic function để ước tính số nước đi còn lại
        private int CalculateHeuristic()
        {
            int heuristic = 0;

            foreach (var tube in tubes)
            {
                if (tube.Count == 0) continue;
                if (IsCompleted(tubes.IndexOf(tube))) continue;

                // Đếm số lần thay đổi màu trong ống
                for (int i = 1; i < tube.Count; i++)
                {
                    if (tube[i] != tube[i - 1])
                    {
                        heuristic++;
                    }
                }
            }

            return heuristic;
        }

        // Giải puzzle bằng BFS tối ưu
        public List<WaterSortMove> Solve()
        {
            visitedStates.Clear();

            if (IsSolved())
                return new List<WaterSortMove>();

            return SolveBFS();
        }

        private List<WaterSortMove> SolveBFS()
        {
            var queue = new Queue<(List<List<int>> state, List<WaterSortMove> moves)>();
            var initialState = GetCurrentState();
            queue.Enqueue((initialState, new List<WaterSortMove>()));

            string initialKey = GetStateKey(initialState);
            visitedStates[initialKey] = 0;

            while (queue.Count > 0)
            {
                var (currentState, currentMoves) = queue.Dequeue();

                // Kiểm tra solution ngay khi dequeue để đảm bảo tìm solution ngắn nhất
                if (IsSolved(currentState))
                {
                    return currentMoves; // Trả về ngay solution đầu tiên (ngắn nhất do BFS)
                }

                // Lấy các nước đi hợp lệ
                var validMoves = GetValidMovesForState(currentState);

                foreach (var (fromTube, toTube) in validMoves)
                {
                    // Tạo state mới bằng cách copy state hiện tại
                    var newState = CloneState(currentState);

                    // Thực hiện nước đi trên state mới
                    int amount = ExecutePourOnState(newState, fromTube, toTube);
                    if (amount == 0) continue;

                    var actualMove = new WaterSortMove(fromTube, toTube, amount);
                    var newMoves = new List<WaterSortMove>(currentMoves) { actualMove };

                    string newStateKey = GetStateKey(newState);

                    // Kiểm tra nếu đã thăm state này với số nước đi ít hơn hoặc bằng
                    if (visitedStates.ContainsKey(newStateKey) && visitedStates[newStateKey] <= newMoves.Count)
                    {
                        continue;
                    }

                    visitedStates[newStateKey] = newMoves.Count;
                    queue.Enqueue((newState, newMoves));
                }
            }

            return null; // Không tìm thấy solution
        }

        // Clone state an toàn
        private List<List<int>> CloneState(List<List<int>> state)
        {
            return state.Select(tube => new List<int>(tube)).ToList();
        }

        // Kiểm tra solved cho một state cụ thể
        private bool IsSolved(List<List<int>> state)
        {
            foreach (var tube in state)
            {
                if (tube.Count == 0) continue;
                if (tube.Count != tubeCapacity) return false;

                int firstColor = tube[0];
                foreach (int color in tube)
                {
                    if (color != firstColor) return false;
                }
            }

            return true;
        }

        // Lấy state key cho một state cụ thể
        private string GetStateKey(List<List<int>> state)
        {
            var stateStrings = new List<string>();
            foreach (var tube in state)
            {
                stateStrings.Add(string.Join(",", tube));
            }

            return string.Join("|", stateStrings);
        }

        // Lấy các nước đi hợp lệ cho một state cụ thể
        private List<(int from, int to)> GetValidMovesForState(List<List<int>> state)
        {
            var moves = new List<(int from, int to)>();

            for (int from = 0; from < state.Count; from++)
            {
                for (int to = 0; to < state.Count; to++)
                {
                    if (CanPourInState(state, from, to) && IsMoveUsefulInState(state, from, to))
                    {
                        moves.Add((from, to));
                    }
                }
            }

            // Sắp xếp theo độ ưu tiên
            return moves.OrderBy(move => GetMovePriorityForState(state, move.from, move.to)).ToList();
        }

        // Kiểm tra có thể pour trong một state cụ thể
        private bool CanPourInState(List<List<int>> state, int fromTube, int toTube)
        {
            if (fromTube == toTube) return false;
            if (state[fromTube].Count == 0) return false;
            if (state[toTube].Count >= tubeCapacity) return false;

            if (state[toTube].Count == 0) return true;

            return state[fromTube][state[fromTube].Count - 1] == state[toTube][state[toTube].Count - 1];
        }

        // Kiểm tra nước đi có hữu ích trong một state cụ thể
        private bool IsMoveUsefulInState(List<List<int>> state, int fromTube, int toTube)
        {
            if (!CanPourInState(state, fromTube, toTube)) return false;

            // Tránh đổ từ ống đã hoàn thành
            if (IsCompletedInState(state, fromTube)) return false;

            // Tránh đổ vào ống đã hoàn thành
            if (IsCompletedInState(state, toTube)) return false;

            // Tránh đổ vào ống rỗng nếu ống nguồn chỉ có 1 màu
            if (state[toTube].Count == 0 && HasSingleColorInState(state, fromTube))
            {
                return false;
            }

            return true;
        }

        // Thực hiện pour trên một state cụ thể
        private int ExecutePourOnState(List<List<int>> state, int fromTube, int toTube)
        {
            if (!CanPourInState(state, fromTube, toTube)) return 0;

            int colorToPour = state[fromTube][state[fromTube].Count - 1];
            int spaceAvailable = tubeCapacity - state[toTube].Count;
            int sameColorCount = CountTopSameColorInState(state, fromTube);
            int amountToPour = Mathf.Min(sameColorCount, spaceAvailable);

            for (int i = 0; i < amountToPour; i++)
            {
                int color = state[fromTube][state[fromTube].Count - 1];
                state[fromTube].RemoveAt(state[fromTube].Count - 1);
                state[toTube].Add(color);
            }

            return amountToPour;
        }

        // Helper methods cho state cụ thể
        private bool IsCompletedInState(List<List<int>> state, int tubeIndex)
        {
            if (state[tubeIndex].Count != tubeCapacity) return false;
            if (state[tubeIndex].Count == 0) return false;

            int firstColor = state[tubeIndex][0];
            return state[tubeIndex].All(color => color == firstColor);
        }

        private bool HasSingleColorInState(List<List<int>> state, int tubeIndex)
        {
            if (state[tubeIndex].Count == 0) return false;
            int firstColor = state[tubeIndex][0];
            return state[tubeIndex].All(color => color == firstColor);
        }

        private int CountTopSameColorInState(List<List<int>> state, int tubeIndex)
        {
            if (state[tubeIndex].Count == 0) return 0;

            int topColor = state[tubeIndex][state[tubeIndex].Count - 1];
            int count = 0;

            for (int i = state[tubeIndex].Count - 1; i >= 0; i--)
            {
                if (state[tubeIndex][i] == topColor)
                    count++;
                else
                    break;
            }

            return count;
        }

        private int GetMovePriorityForState(List<List<int>> state, int fromTube, int toTube)
        {
            int priority = 0;

            // Ưu tiên hoàn thành ống
            if (state[toTube].Count > 0)
            {
                int sameColorCount = CountTopSameColorInState(state, toTube);
                int availableSpace = tubeCapacity - state[toTube].Count;
                int sourceColorCount = CountTopSameColorInState(state, fromTube);

                if (sameColorCount + Mathf.Min(sourceColorCount, availableSpace) == tubeCapacity)
                {
                    priority -= 1000;
                }
            }

            // Ưu tiên đổ vào ống có nhiều cùng màu
            if (state[toTube].Count > 0)
            {
                priority -= CountTopSameColorInState(state, toTube) * 10;
            }

            // Tránh đổ vào ống rỗng trừ khi cần thiết
            if (state[toTube].Count == 0)
            {
                priority += 50;
            }

            return priority;
        }

        // Lấy các nước đi được ưu tiên (sắp xếp theo độ hữu ích)
        private List<WaterSortMove> GetPrioritizedMoves()
        {
            var moves = new List<WaterSortMove>();

            for (int from = 0; from < tubes.Count; from++)
            {
                for (int to = 0; to < tubes.Count; to++)
                {
                    if (IsMoveUseful(from, to))
                    {
                        moves.Add(new WaterSortMove(from, to, 0)); // amount sẽ được tính sau
                    }
                }
            }

            // Sắp xếp theo độ ưu tiên
            return moves.OrderBy(move => GetMovePriority(move)).ToList();
        }

        // Tính độ ưu tiên của nước đi (số càng nhỏ càng ưu tiên)
        private int GetMovePriority(WaterSortMove move)
        {
            int priority = 0;

            // Ưu tiên 1: Hoàn thành ống
            if (tubes[move.toTube].Count > 0)
            {
                int topColor = tubes[move.toTube][tubes[move.toTube].Count - 1];
                int sameColorCount = CountTopSameColor(move.toTube);
                int availableSpace = tubeCapacity - tubes[move.toTube].Count;
                int sourceColorCount = CountTopSameColor(move.fromTube);

                if (sameColorCount + Mathf.Min(sourceColorCount, availableSpace) == tubeCapacity)
                {
                    priority -= 1000; // Ưu tiên cao nhất
                }
            }

            // Ưu tiên 2: Đổ vào ống có nhiều cùng màu
            if (tubes[move.toTube].Count > 0)
            {
                priority -= CountTopSameColor(move.toTube) * 10;
            }

            // Ưu tiên 3: Đổ từ ống có ít màu khác nhau
            priority += GetColorVariety(move.fromTube) * 5;

            // Ưu tiên 4: Tránh đổ vào ống rỗng trừ khi cần thiết
            if (tubes[move.toTube].Count == 0)
            {
                priority += 50;
            }

            return priority;
        }

        // Đếm số loại màu khác nhau trong ống
        private int GetColorVariety(int tubeIndex)
        {
            if (tubes[tubeIndex].Count == 0) return 0;
            return tubes[tubeIndex].Distinct().Count();
        }

        // Thực hiện một nước đi cụ thể
        public bool ExecuteMove(WaterSortMove move)
        {
            if (move.fromTube < 0 || move.fromTube >= tubes.Count ||
                move.toTube < 0 || move.toTube >= tubes.Count)
                return false;

            if (!CanPour(move.fromTube, move.toTube))
                return false;

            int actualAmount = Pour(move.fromTube, move.toTube);
            return actualAmount > 0;
        }

        // Kiểm tra xem một nước đi có hợp lệ không
        public bool IsValidMove(int fromTube, int toTube)
        {
            if (fromTube < 0 || fromTube >= tubes.Count ||
                toTube < 0 || toTube >= tubes.Count)
                return false;

            return CanPour(fromTube, toTube);
        }

        // Lấy tất cả các nước đi hợp lệ
        public List<WaterSortMove> GetValidMoves()
        {
            var validMoves = new List<WaterSortMove>();

            for (int from = 0; from < tubes.Count; from++)
            {
                for (int to = 0; to < tubes.Count; to++)
                {
                    if (CanPour(from, to))
                    {
                        int amount = Mathf.Min(CountTopSameColor(from), tubeCapacity - tubes[to].Count);
                        validMoves.Add(new WaterSortMove(from, to, amount));
                    }
                }
            }

            return validMoves;
        }

        // Reset puzzle về trạng thái ban đầu
        public void Reset(int[][] initialState)
        {
            InitializePuzzle(initialState);
            bestSolution.Clear();
            bestSolutionLength = int.MaxValue;
            visitedStates.Clear();
        }

        // Lấy thông tin debug
        public string GetDebugInfo()
        {
            string info = "=== Water Sort Puzzle State ===\n";
            for (int i = 0; i < tubes.Count; i++)
            {
                info += $"Tube {i}: [{string.Join(", ", tubes[i])}]";
                if (IsCompleted(i)) info += " (COMPLETED)";
                info += "\n";
            }

            info += $"Is Solved: {IsSolved()}\n";
            info += $"Valid Moves: {GetValidMoves().Count}\n";
            info += $"Heuristic: {CalculateHeuristic()}\n";
            return info;
        }
    }

// Utility class để tạo puzzle ngẫu nhiên
    public static class WaterSortPuzzleGenerator
    {
        public static int[][] GenerateRandomPuzzle(int numColors, int numTubes, int tubeCapacity, int numEmptyTubes = 2)
        {
            var puzzle = new List<List<int>>();
            var allColors = new List<int>();

            // Tạo danh sách tất cả màu cần đổ
            for (int color = 1; color <= numColors; color++)
            {
                for (int i = 0; i < tubeCapacity; i++)
                {
                    allColors.Add(color);
                }
            }

            // Xáo trộn màu
            for (int i = 0; i < allColors.Count; i++)
            {
                int randomIndex = Random.Range(i, allColors.Count);
                int temp = allColors[i];
                allColors[i] = allColors[randomIndex];
                allColors[randomIndex] = temp;
            }

            // Phân chia màu vào các ống
            int colorIndex = 0;
            for (int tube = 0; tube < numTubes - numEmptyTubes; tube++)
            {
                var tubeColors = new List<int>();
                for (int i = 0; i < tubeCapacity && colorIndex < allColors.Count; i++)
                {
                    tubeColors.Add(allColors[colorIndex]);
                    colorIndex++;
                }

                puzzle.Add(tubeColors);
            }

            // Thêm ống rỗng
            for (int i = 0; i < numEmptyTubes; i++)
            {
                puzzle.Add(new List<int>());
            }

            return puzzle.Select(tube => tube.ToArray()).ToArray();
        }
    }
}