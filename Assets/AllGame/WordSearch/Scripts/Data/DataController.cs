using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Asset;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.WordSearch.Scripts.Data
{
    public class DataController : BhMonoBehavior
    {
        [SerializeField]
        private List<DataResourcePath> levelResourcePathMap;
        
        [Header("Diff")]
        [SerializeField]
        private int minWordCount = 4;
        
        [SerializeField]
        private int maxWordCount = 12;
        
        [SerializeField]
        private int minBoardSize = 5;
        
        [SerializeField]
        private int maxBoardSize = 15;
        
        
        private TextAsset _currentLevelData;

        private void OnEnable()
        {
            if (Evm)
            {
                Evm.OnCompleteChangeLanguage.AddListener(GetTextAssetInstant);
            }
        }
        
        private void OnDisable()
        {
            if (Evm)
            {
                Evm.OnCompleteChangeLanguage.RemoveListener(GetTextAssetInstant);
            }
        }
        
        /// <summary>
        /// Lấy dữ liệu level dựa trên chỉ số
        /// </summary>
        /// <param name="levelIndex">Chỉ số level (bắt đầu từ 0)</param>
        /// <returns>Dữ liệu level hoặc null nếu chỉ số không hợp lệ</returns>
        public LevelData GetLevel(int levelIndex)
        {
            var levelDiff = GetLevelDifficult(levelIndex);
            
            return CreateLevel(levelDiff.wordCount, levelDiff.boardSize);
        }

        
        void GetTextAssetInstant()
        {
            GetTextAsset(true);
        }
        

        void GetTextAsset(bool forceUpdate = false)
        {
            if (_currentLevelData&& !forceUpdate)
            {
                return;
            }

            if (_currentLevelData)
            {
                AssetManager.UnloadAsset(_currentLevelData);
            }
            
            var resourcePath = levelResourcePathMap[0].resourcePaths.GetRandom();

            var data = levelResourcePathMap.FirstOrDefault(x => x.languageType == Gm.data.setting.currentLanguage);
            if (data != null)
            {
                resourcePath = data.resourcePaths.GetRandom();
            }

            /*if (levelResourcePathMap.TryGetValue(Gm.data.setting.currentLanguage, out var listPath))
            {
                resourcePath = listPath.GetRandom();
            }*/
            
            _currentLevelData = AssetManager.LoadAsset<TextAsset>(resourcePath);
        }

        LevelData CreateLevel(int wordCount, int boardSize)
        {
            GetTextAsset();
            string[] lines = _currentLevelData.text.Split(new[] { '\r', '\n' }, System.StringSplitOptions.RemoveEmptyEntries);
            
            /*
            BhDebug.Log("Line count: " + lines.Length);

            foreach (var line in lines)
            {
                BhDebug.Log("Line: " + line);
            }
            */
            
            var correctBoardSize = boardSize;
            var level = GetLevelFromFile();


            while (level == null)
            {
                correctBoardSize++;
                level = GetLevelFromFile();
            }

            return level;
            
            LevelData GetLevelFromFile()
            {
                var shuffleLines = lines.ToList().Clone().Shuffle(null).ToList();
                
                for (int i = 0; i < shuffleLines.Count; i++)
                {
                    string line = shuffleLines[i].Trim();
                    if (!string.IsNullOrEmpty(line))
                    {
                        // Tạo dữ liệu level từ mỗi dòng
                        LevelData level = new LevelData(line, wordCount, correctBoardSize);

                        if (level.IsLevelCreateCompleted)
                        {
                            return level;
                        }
                    }
                }

                return null;
            }
        }
        
        
        public (int wordCount, int boardSize) GetLevelDifficult(int levelIndex)
        {
            // Đảm bảo levelIndex nằm trong khoảng hợp lệ
            levelIndex = Mathf.Max(1, levelIndex);
            
            // Các mốc level quan trọng
            const int easyLevel = 1;
            const int mediumLevel = 100;
            const int hardLevel = 500;
            
            float difficultyFactor;
            
            if (levelIndex <= mediumLevel)
            {
                // Từ level 1 đến 200: độ khó tăng 70%
                difficultyFactor = Mathf.Lerp(0f, 0.9f, (float)(levelIndex - easyLevel) / (mediumLevel - easyLevel));
            }
            else
            {
                // Từ level 200 đến 1000: độ khó tăng từ 70% đến 100%
                difficultyFactor = Mathf.Lerp(0.9f, 1f, (float)(levelIndex - mediumLevel) / (hardLevel - mediumLevel));
            }
            
            // Tính toán độ khó dựa trên hàm phi tuyến
            // Sử dụng hàm bậc 2 để tạo đường cong độ khó
            float wordDifficultyFactor = difficultyFactor * difficultyFactor;
            float boardDifficultyFactor = Mathf.Sqrt(difficultyFactor);
            
            // Kết hợp các yếu tố khác nhau cho từng level
            if (levelIndex % 5 == 0)
            {
                // Cứ 5 level tăng đột biến số từ
                wordDifficultyFactor = Mathf.Min(1.0f, wordDifficultyFactor * 1.2f);
            }
            
            if (levelIndex % 10 == 0)
            {
                // Cứ 10 level tăng đột biến kích thước bảng
                boardDifficultyFactor = Mathf.Min(1.0f, boardDifficultyFactor * 1.15f);
            }
            
            // Tính số từ dựa trên độ khó
            int wordCount = Mathf.RoundToInt(Mathf.Lerp(minWordCount, maxWordCount, wordDifficultyFactor));
            
            // Tính kích thước bảng dựa trên độ khó
            int boardSize = Mathf.RoundToInt(Mathf.Lerp(minBoardSize, maxBoardSize, boardDifficultyFactor));
            
            // Đảm bảo giá trị nằm trong khoảng cho phép
            wordCount = Mathf.Clamp(wordCount, minWordCount, maxWordCount);
            boardSize = Mathf.Clamp(boardSize, minBoardSize, maxBoardSize);
            
            // Đảm bảo kích thước bảng đủ lớn cho số từ
            int minRequiredSize = Mathf.CeilToInt(Mathf.Sqrt(wordCount * 2.5f));
            boardSize = Mathf.Max(boardSize, minRequiredSize);
            boardSize = Mathf.Clamp(boardSize, minBoardSize, maxBoardSize);
            
            return (wordCount, boardSize);
        }


#if UNITY_EDITOR
        [Button]
        void GetAllResources()
        {
            // Đường dẫn gốc đến thư mục chứa dữ liệu level
            string basePath = "Assets/Game/WordSearch/Resources/WordSearchData";
            
            // Kiểm tra xem thư mục có tồn tại không
            if (!System.IO.Directory.Exists(basePath))
            {
                BhDebug.LogError($"Base directory not found: {basePath}");
                return;
            }
            
            // Lấy tất cả các thư mục con trong thư mục gốc
            string[] directories = System.IO.Directory.GetDirectories(basePath);

            foreach (LanguageType languageType in Enum.GetValues(typeof(LanguageType)))
            {
                var relativePath = directories.FirstOrDefault(x =>
                    new System.IO.DirectoryInfo(x).Name.Equals(languageType.ToString(),
                        StringComparison.OrdinalIgnoreCase));

                if (relativePath != null)
                {
                    var allTextAsset = UtilitiesTool
                        .GetResources<TextAsset>(relativePath, new List<string>() { ".json" , ".txt" });

                    var stringPathList = allTextAsset.Select(x => UtilitiesTool.GetResourcePath(x)).ToList();

                    levelResourcePathMap.Add(new DataResourcePath(languageType, stringPathList));

                }
                else
                {
                }

                // Thêm thông tin vào dictionary
            }

        }
#endif


        [Serializable]
        public class DataResourcePath
        {
            public LanguageType languageType;
            public List<string> resourcePaths;

            public DataResourcePath(LanguageType languageType, List<string> resourcePaths)
            {
                this.languageType = languageType;
                this.resourcePaths = resourcePaths;
            }
        }
    }
}