using System;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using Game.WordSearch.Scripts.Data;
using UnityEngine;
using System.Collections;
using System.Linq;
using b100SDK.Scripts.Audio;
using b100SDK.Scripts.EventHandler.Events;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Vibration;
using DG.Tweening;
using Sirenix.OdinInspector;
using Grid = AllGame.Common.Scripts.Grid;

namespace Game.WordSearch.Scripts.Gameplay
{
    public class BoardController : BhMonoBehavior
    {
        [SerializeField]
        private RectTransform gridRectTransform;

        [SerializeField] 
        private GameplayController gameplayController;
        
        [SerializeField]
        private Vector2 gridSpacing;

        [SerializeField]
        private Vector2 gridPadding;
        
        [Header("Rotate")]
        [SerializeField]
        private float rotateDuration = 1.0f;
        
        [SerializeField]
        private GameObject coverRotate;
        
        [Header("Events")]
        [SerializeField]
        private VoidEventChannel onRotateEvent;
        
        
        private Grid _grid;
        private List<CharacterItem> _characters = new();

        private bool _isRotating;

        private void OnEnable()
        {
            onRotateEvent.OnEventRaised += RotateBoard180Degrees;
        }
        
        private void OnDisable()
        {
            onRotateEvent.OnEventRaised -= RotateBoard180Degrees;
        }


        public void InitBoard(LevelData levelData)
        {
            // Clear previous characters
            WordSearchPoolManager.Instance.ClearPoolCharacter();
            _characters.Clear();
            
            // Create grid
            _grid = new Grid(levelData.GetRows(), levelData.GetColumns(), gridSpacing, gridPadding, gridRectTransform.rect.size, true);
            
            transform.eulerAngles = Vector3.zero;

            // Initialize new characters
            for (int i = 0; i < levelData.GetRows(); i++)
            {
                for (int j = 0; j < levelData.GetColumns(); j++)
                {
                    var newCharacter = WordSearchPoolManager.Instance.GetCharacterItem();
                    newCharacter.Init((i, j), levelData.GetLetterAt(i, j), _grid.CellSize);
                    
                    newCharacter.transform.SetParent(gridRectTransform);
                    newCharacter.transform.localPosition = _grid.GetGridPosition((i, j));
                    newCharacter.transform.eulerAngles = Vector3.zero;
                    
                    _characters.Add(newCharacter);
                }
            }

            _isRotating = false;
            coverRotate.SetActiveWithChecker(false);
        }

        public CharacterItem GetCharacterAt(int currentRow, int currentCol)
        {
            foreach (var characterItem in _characters)
            {
                if (characterItem.GetIndex().i == currentRow && characterItem.GetIndex().j == currentCol)
                {
                    return characterItem;
                }
            }

            return null;
        }

        public void ShowHint((int i, int j) indexOfCharacter)
        {
            var character = GetCharacterAt(indexOfCharacter.i, indexOfCharacter.j);
            
            if (character != null)
            {
                character.ShowHint();
            }
        }
        
        /// <summary>
        /// Xoay bảng chữ 180 độ và giữ các chữ cái luôn đọc được trong quá trình xoay
        /// </summary>
        
        [Button]
        public void RotateBoard180Degrees()
        {
            if (_grid == null || _characters.Count == 0)
            {
                Debug.LogWarning("Không thể xoay bảng vì bảng chưa được khởi tạo.");
                return;
            }
            
            if (_isRotating)
            {
                Debug.LogWarning("Đang xoay bảng, không thể thực hiện thêm thao tác xoay khác.");
                return;
            }
            
            coverRotate.SetActiveWithChecker(true);
            _isRotating = true;
            
            // Bắt đầu animation xoay
            transform.DOKill();
            transform.DOLocalRotate(new Vector3(0, 0, 180), rotateDuration)
                .SetRelative(true)
                .OnComplete(() =>
                {
                    _isRotating = false;
                    coverRotate.SetActiveWithChecker(false);
                });

            foreach (var character in _characters)
            {
                character.transform.DOKill();
                character.transform.DOLocalRotate(new Vector3(0, 0, -180), rotateDuration)
                    .SetRelative(true);
            }
        }
        
        
        public Vector2 GetCellSize() => _grid.CellSize;


        public CharacterItem GetCharacterItemCanConnect(CharacterItem startCharacterItem, Vector3 wordPosition)
        {
            var correctPosition = gridRectTransform.InverseTransformPoint(wordPosition);
            return GetCharacterAtPosition(startCharacterItem, correctPosition);
        }

        public CharacterItem GetCharacterAtPosition(CharacterItem startCharacterItem, Vector2 position)
        {
            var index = _grid.GetIndex(position);
            
            //BhDebug.Log("Index: " + index);

            if (index != (-1, -1))
            {
                var characterItem = _characters.FirstOrDefault(x => x.GetIndex() == index);
                if (characterItem == null) {
                    return null;
                }

                // Nếu không thể kết nối trực tiếp với characterItem, tìm item gần nhất có thể kết nối
                if (!gameplayController.CanConnect(startCharacterItem, characterItem))
                {
                    var connectableItems = _characters.Where(x => gameplayController.CanConnect(startCharacterItem, x)).ToList();
                    if (connectableItems.Any())
                    {
                        // Tìm item có khoảng cách index gần nhất với vị trí được click
                        var targetIndex = index;
                        var nearestCharacterItem = connectableItems
                            .OrderBy(x => Vector2.Distance(new Vector2(x.GetIndex().Item1, x.GetIndex().Item2), 
                                                         new Vector2(targetIndex.Item1, targetIndex.Item2)))
                            //.Take(9)
                            .ThenBy(x => Vector2.Distance(position, x.transform.position))
                            .First();
                        
                        //BhDebug.Log("Nearest Character: " + nearestCharacterItem.GetCharacter());

                        return nearestCharacterItem;
                    }
                }
                else
                {
                  return characterItem;
                }
            }

            return null;
        }


        public bool IsCharacterItemInEdge(CharacterItem characterItem)
        {
            return _grid.IsInEdge(characterItem.GetIndex());
        }
        
        public bool IsCharacterItemInEdge(CharacterItem characterItem, LineItem.LineDirection currentDirection)
        {
            var isInGridEdge = IsCharacterItemInEdge(characterItem);

            if (!isInGridEdge)
            {
                return false;
            }

            switch (currentDirection)
            {
                case LineItem.LineDirection.Horizontal:
                    return characterItem.GetIndex().j == 0 || characterItem.GetIndex().j == _grid.Columns - 1;
                    break;

                case LineItem.LineDirection.Vertical:
                    return characterItem.GetIndex().i == 0 || characterItem.GetIndex().i == _grid.Rows - 1;
                    break;

                case LineItem.LineDirection.DiagonalUp:
                    return characterItem.GetIndex().i == 0 || characterItem.GetIndex().i == _grid.Rows - 1 ||
                           characterItem.GetIndex().j == 0 || characterItem.GetIndex().j == _grid.Columns - 1;
                    break;

                case LineItem.LineDirection.DiagonalDown:
                    return characterItem.GetIndex().i == 0 || characterItem.GetIndex().i == _grid.Rows - 1 ||
                           characterItem.GetIndex().j == 0 || characterItem.GetIndex().j == _grid.Columns - 1;
                    break;
            }

            return false;


        }
    }
}