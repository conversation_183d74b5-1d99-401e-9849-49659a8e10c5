using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Game.WordSearch.Scripts.Gameplay
{
    public class CurrentTextContainer : BhMonoBehavior
    {
        [SerializeField]
        private RectTransform rectTransform;
        
        [SerializeField]
        private TMP_Text currentText;
        
        [SerializeField]
        private Image colorImage;
        
        [SerializeField]
        private float changeSizeDuration = .2f;

        [SerializeField]
        private float hideDuration = .2f;

        [SerializeField]
        private float offsetSize = 100f;
        
        public void SetColor(Color color)
        {
            colorImage.color = color;
        }

        public void Hide(bool instant = false, bool hideTextTween = true)
        {
            if (instant)
            {
                rectTransform.DOKill();
                rectTransform.sizeDelta = new Vector2(0f, rectTransform.sizeDelta.y);
                currentText.text = "";
            }
            else
            {
                rectTransform.DOKill();
                rectTransform.DOSizeDelta(new Vector2(0f, rectTransform.sizeDelta.y), hideDuration)
                    .SetTarget(rectTransform);

                if (hideTextTween)
                {
                    string original = currentText.text;
                    int totalLength = original.Length;
                    float interval = hideDuration / totalLength;

                    // Sequence để giảm ký tự từng bước
                    Sequence seq = DOTween.Sequence();

                    for (int i = 0; i < totalLength; i++)
                    {
                        int remaining = totalLength - i;
                        seq.AppendCallback(() =>
                        {
                            currentText.text = original.Substring(0, remaining - 1);
                        });
                        seq.AppendInterval(interval);
                    }
                }
            }
        }

        public void ClearText()
        {
            currentText.text = "";
        }

        public void SetText(string word)
        {
            currentText.text = word;
            
            if (string.IsNullOrEmpty(word))
            {
                Hide();
                return;
            }
            
            var targetSize = currentText.GetPreferredValues().x + offsetSize;
            rectTransform.DOKill();
            rectTransform.DOSizeDelta(new Vector2(targetSize, rectTransform.sizeDelta.y), changeSizeDuration)
                .SetTarget(rectTransform);
        }


        public List<(char letter, Vector3 position)> GetTextPosition()
        {
            currentText.ForceMeshUpdate();
            
            var result = new List<(char letter, Vector3 position)>();
            
            for (int i = 0; i < currentText.text.Length; i++)
            {
                var letter = currentText.text[i];
                var charInfo = currentText.textInfo.characterInfo[i];
                
                // Tính toán vị trí trung tâm của ký tự trong không gian mesh
                Vector3 centerOfChar = (charInfo.bottomLeft + charInfo.topRight) / 2f;
                
                // Chuyển đổi từ tọa độ mesh sang tọa độ world
                Vector3 position = currentText.transform.TransformPoint(centerOfChar);
                
                result.Add((letter, position));
            }
            
            return result;
        }
        
        
        public float GetFontSize()
        {
            return currentText.fontSize;
        }
    }
}