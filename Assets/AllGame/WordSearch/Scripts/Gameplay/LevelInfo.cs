using b100SDK.Scripts.Base;
using b100SDK.Scripts.Localize;
using TMPro;
using UnityEngine;

namespace Game.WordSearch.Scripts.Gameplay
{
    public class LevelInfo : BhMonoBehavior
    {
        [SerializeField]
        private LocalizeText levelText;
        
        [SerializeField]
        private LocalizeText descText;
        
        
        public void Init(int level)
        {
            levelText.SetText("level", level.ToString());
            descText.SetText("rate", CalculatePassRate(level).ToString("F2") + "%");
        }

        /// <summary>
        /// Tính tỉ lệ người vượt qua level dựa trên hàm easeOutExpo
        /// Tỉ lệ giảm nhanh ở đoạn đầu và giảm chậm ở đoạn sau
        /// Tỉ lệ giảm dần từ 99% (level 1) xuống 30% (level maxLevel)
        /// <PERSON><PERSON> thêm yếu tố ngẫu nhiên để kết quả trông tự nhiên hơn
        /// </summary>
        private float CalculatePassRate(int level, int maxLevel = 1000)
        {
            // Đảm bảo level nằm trong khoảng hợp lệ
            level = Mathf.Clamp(level, 1, maxLevel);
            
            // Chuẩn hóa level về đoạn [0, 1]
            float normalizedLevel = (float)(level - 1) / (maxLevel - 1);
            
            // Sử dụng hàm easeOutExpo để tính tỉ lệ
            // Hàm này giảm nhanh ở đầu và giảm chậm ở cuối
            float t = normalizedLevel;
            float startValue = 99f;
            float endValue = 30f;
            float change = endValue - startValue;
            
            // Công thức easeOutExpo: f(t) = c * (-2^(-10 * t) + 1) + b
            float passRate = change * (-Mathf.Pow(2, -10 * t) + 1) + startValue;
            
            // Thêm yếu tố ngẫu nhiên (±1.5%) để kết quả trông tự nhiên hơn
            float randomFactor = Random.Range(-1.5f, 1.5f);
            passRate += randomFactor;
            
            // Đảm bảo giá trị nằm trong khoảng hợp lý
            passRate = Mathf.Clamp(passRate, 30f, 99f);
            
            return passRate;
        }
    }
}