%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: RAM_Mono_Graph
  m_Shader: {fileID: 4800000, guid: bc65170c051b0724287a7f1636d87573, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 5
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 4500
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - FpsValues_Length: 120
    - PixelSnap: 0
    - ScrollSpeed: 0.2
    - _AmazingThreshold: 0.72
    - _BumpScale: 1
    - _CautionThreshold: 0
    - _ColorMask: 15
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _Glossiness: 0.5
    - _GoodThreshold: 0.2
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _UVSec: 0
    - _UseUIAlphaClip: 0
    - _ZWrite: 1
    m_Colors:
    - _AmazingColor: {r: 0, g: 1, b: 1, a: 1}
    - _CautionColor: {r: 0, g: 0.73333335, b: 0.9764706, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _CriticalColor: {r: 0, g: 0.73333335, b: 0.9764706, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _GoodColor: {r: 0, g: 0.73333335, b: 0.9764706, a: 1}
