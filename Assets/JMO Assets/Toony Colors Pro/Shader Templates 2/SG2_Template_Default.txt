// Toony Colors Pro+Mobile 2
// (c) 2014-2023 <PERSON>

#NAME=Default
#ID=TEMPLATE_DEFAULT
#SG2

#================================================================

#MODULES

# needed by Shader Generator 2:
NoTile Sampling
Triplanar Sampling
HSV
Screen Space UV
Hash Functions
Dithering

# lighting:
Ramp Shading
ShadowHSV
Specular
Rim Lighting
Reflection
Subsurface Scattering
MatCap
Custom Ambient

# surface:
AlbedoHSV
Normal Mapping
Texture Blending
Triplanar
Vertex Displacement
Terrain

# stylization:
NdotL Stylization
Sketch
Outline

# special effects:
Dissolve
Vertical Fog
Wind
Water
VertExmotion
CurvedWorld
Aura2
Depth Texture

#END

#================================================================

#FEATURES

[[MODULE:FEATURES:Screen Space UV]]
dd_start	lbl="LIGHTING"
---
[[MODULE:FEATURES:Ramp Shading]]
#sngl	lbl="Bypass Additional Lights Falloff"		kw=BYPASS_LIGHT_FALLOFF								tt="Bypass the point and spot light falloff calculation and only use the TCP2 ramp shading instead"
---
mult	lbl="Shadow Color Mode"			kw=Multiply|,Replace Color|SHADOW_COLOR_LERP					tt="How to blend the shadow color on the model"		help="featuresreference/lighting/shadowcolormode"
[[MODULE:FEATURES:ShadowHSV]]
sngl	lbl="Apply Shadows before Ramp"		kw=ATTEN_AT_NDL												tt="Apply the shadow map attenuation before calculating the shading ramp"	help="featuresreference/lighting/applyshadowsbeforeramp"
---
[[MODULE:FEATURES:Specular]]
---
sngl	lbl="Emission"					kw=EMISSION		help="featuresreference/lighting/emission"		tt="Adds emission to the shader.  Modify the Emission property to change the source input (color, texture, etc.) and possibly use a mask."
---
[[MODULE:FEATURES:Rim Lighting]]
---
[[MODULE:FEATURES:Subsurface Scattering]]
---
subh	lbl="Reflections"		help="featuresreference/lighting/reflections"
sngl	lbl="Reflection Probes"			kw=GLOSSY_REFLECTIONS																					tt="Enable reflection probes or skybox reflection support"
[[MODULE:FEATURES:Reflection]]
sngl	lbl="Make Reflections Optional"	kw=REFLECTION_SHADER_FEATURE		needsOr=REFLECTION_CUBEMAP,GLOSSY_REFLECTIONS,PLANAR_REFLECTION		tt="Will make reflections optional in the material inspector, using a shader keyword"
---
[[MODULE:FEATURES:MatCap]]
---
subh	lbl="Ambient Lighting/Indirect Diffuse"		help="featuresreference/lighting/ambientlighting/indirectdiffuse"
flag	lbl="Disable Ambient Lighting"	kw=noambient	block="pragma_surface_shader"	tt="Disable ambient lighting calculated by the engine (as defined in the Lighting Settings)"
[[MODULE:FEATURES:Custom Ambient]]
sngl	lbl="Occlusion"					kw=OCCLUSION		tt="Adds ambient lighting occlusion support.  Modify the Occlusion property to change the source input (texture, vertex color, etc.)."
sngl	lbl="Make Ambient Optional"		kw=AMBIENT_SHADER_FEATURE	tt="Will make ambient/indirect diffuse lighting optional in the material inspector, using a shader keyword"
---
dd_end
dd_start	lbl="SURFACE"
---
[[MODULE:FEATURES:Vertex Displacement]]
---
[[MODULE:FEATURES:AlbedoHSV]]
---
[[MODULE:FEATURES:Normal Mapping]]
sngl	lbl="Sample Normal Map first"	kw=WORLD_NORMAL_FROM_BUMP		needs=BUMP	indent		tt="Sample the Normal Map before other Shader Properties, so that the ones using the 'World Normal' value will use the normal mapped version"
---
[[MODULE:FEATURES:Texture Blending]]
---
[[MODULE:FEATURES:Triplanar]]
dd_end
dd_start	lbl="STYLIZATION"
---
[[MODULE:FEATURES:NdotL Stylization]]
[[MODULE:FEATURES:Sketch]]
[[MODULE:FEATURES:Outline]]
dd_end
dd_start	lbl="TERRAIN"
---
[[MODULE:FEATURES:Terrain]]
dd_end
dd_start	lbl="SPECIAL EFFECTS"
---
sngl	lbl="Custom Time"				kw=CUSTOM_TIME		help="featuresreference/specialeffects/customtime"		tt="Use a custom time variable controlled by scripts for all timed-related features (this will disable Editor animation preview).  The custom time variable is a float4 named '_CustomTime'.  By default, the Y component should reflect the actual time, and other components are multiples of that value:  x: time * 0.05  y: time  z: time * 2  w: time * 3"
sngl	lbl="Local"		indent			kw=CUSTOM_TIME_LOCAL	needs=CUSTOM_TIME		help="featuresreference/specialeffects/customtime"		tt="Define the custom time value locally to the material, instead of globally.  Use Shader.SetGlobalVector if global, else material.SetVector if local."
---
sngl	lbl="Silhouette Pass"			kw=PASS_SILHOUETTE		help="featuresreference/specialeffects/silhouettepass"		tt="Adds a silhouette pass, to show the object when it is behind obstacles"
sngl	lbl="Stencil Mask"				kw=SILHOUETTE_STENCIL	needs=PASS_SILHOUETTE	indent	tt="Use the Stencil Buffer as a mask for the silhouette, to prevent transparency issues with non-convex meshes or multiple meshes"
---
[[MODULE:FEATURES:Wind]]
---
[[MODULE:FEATURES:Dissolve]]
---
[[MODULE:FEATURES:Vertical Fog]]
---
[[MODULE:FEATURES:Water]]
dd_end
dd_start	lbl="TRANSPARENCY/BLENDING"
---
sngl	lbl="Auto Optional Transparency"	kw=AUTO_TRANSPARENT_BLENDING	help="featuresreference/transparency/blending/autooptionaltransparency"		tt="Automatically handle transparency and add rendering mode options in the material inspector (opaque, transparent, fade), similar to how it is handled in the Hybrid Shader"
---
mult	lbl="Blending"			excl=AUTO_TRANSPARENT_BLENDING		kw=Off|,Alpha Blending|ALPHA_BLENDING,Alpha Blending Premultiplied|ALPHA_BLENDING_PREMULT,Additive|ADDITIVE_BLENDING,Multiplicative|MULTIPLICATIVE_BLENDING,Custom Blending|CUSTOM_BLENDING	toggles=SHADER_BLENDING		tt="Enable blending on the shader"	help="featuresreference/transparency/blending/blending/blendoperation"
mult_fs		lbl="Blend Operation"		kw=BLEND_OP		options=Default|,Custom|Constant,Material Property|Material Property		shader_property="Blend Operation"		tt="Enable blend operation control"		help="featuresreference/transparency/blending/blending/blendoperation"
warning	msgType=info	needs=CUSTOM_BLENDING	lbl="Look at the <b>Shader Properties</b> tab to setup the custom blending states."
space	space=4			needs=CUSTOM_BLENDING
warning	msgType=warning				needs=CUSTOM_BLENDING					lbl="The Custom Blending factors will only apply to the <b>Forward Base</b> pass (main directional light).  <b>Forward Add</b> passes (additional lights) will use <b>One One</b> as the blending factors.  This is unfortunately a surface shader limitation."
space	space=4			needs=CUSTOM_BLENDING
---
sngl	lbl="Depth pre-pass"		kw=DEPTH_PREPASS		tt="Adds a depth only shader pass, to prevent parts of the mesh from being visible through itself."		help="featuresreference/transparency/blending/depthpre-pass"
---
sngl	lbl="Alpha Testing (Cutout)"	kw=ALPHA_TESTING			help="featuresreference/transparency/blending/alphatesting"
sngl	lbl="Alpha to Coverage"			kw=ALPHA_TO_COVERAGE		needs=ALPHA_TESTING							indent		tt="Enables Alpha to Coverage, which allows MSAA anti-aliasing to be used with alpha testing"
sngl	lbl="Disable alpha sharpening"	kw=ALPHA_TO_COVERAGE_RAW	needs=ALPHA_TESTING,ALPHA_TO_COVERAGE		indent=2	tt="Disables screen-space alpha sharpening, which is used to get proper anti-aliasing with Alpha to Coverage"
sngl	lbl="Transparency Dithering"	kw=ALPHA_TESTING_DITHERING	needs=ALPHA_TESTING							indent		tt="Enables dithering for alpha testing, i.e. making the transparency transition pixel by pixel in screen space"
mult	lbl="Dithering Pattern"			kw=Procedural 4x4 Pixels|,Procedural 8x8 Pixels|ALPHA_DITHER_8x8,Lookup Texture|ALPHA_DITHER_TEXTURE	indent=2	needs=ALPHA_TESTING,ALPHA_TESTING_DITHERING	tt="The dithering pattern to use, either calculated with maths or using a lookup texture."
---
sngl		lbl="Dithered Shadows"		kw=DITHERED_SHADOWS			tt="Enables dithered shadows for transparent materials"
dd_end
dd_start	lbl="SHADER STATES"
---
subh		lbl="Shader States"		help="featuresreference/shaderstates"
---
mult_fs		lbl="Face Culling (Double-sided)"	excl=AUTO_TRANSPARENT_BLENDING	kw=CULLING	options=Default|,Custom|Constant,Material Property|Material Property	shader_property="Face Culling"	tt="Enable face culling control"
mult		lbl="Backface Lighting"		kw=Off|,Flip Normal (Z)|BACKFACE_LIGHTING_Z,Flip Normal (XYZ)|BACKFACE_LIGHTING_XYZ		tt="Invert the normals on backfaces for accurate lighting calculation (this may not work properly with shadows and introduce other artifacts)"
---
mult_fs		lbl="Depth Write"	excl=AUTO_TRANSPARENT_BLENDING		kw=ZWRITE	options=Default|,Custom|Constant,Material Property|Material Property	shader_property="Depth Write"	tt="Enable depth write (ZWrite) value control"
mult_fs		lbl="Depth Test"										kw=ZTEST	options=Default|,Custom|Constant,Material Property|Material Property	shader_property="Depth Test"	tt="Enable depth test (ZTest) control"
---
mult	lbl="Stencil"						kw=Off|,Custom|STENCIL				tt="Enable stencil control (see the Properties tab)"
warning	msgType=info	needs=STENCIL	lbl="Look at the <b>Shader Properties</b> tab to setup the stencil states."
space	space=4			needs=STENCIL
---
keyword	lbl="Shader Target"	kw=SHADER_TARGET	forceKeyword=true	values=2.0 (Old hardware)|2.0,2.5 (Low-end mobile devices)|2.5,3.0 (Recommended default)|3.0,3.5|3.5,4.0|4.0,4.5|4.5,4.6|4.6,5.0|5.0		default=2
warning	msgType=info		lbl="Use <b>Shader Target 2.5</b> for maximum compatibility across mobile devices (OpenGL ES 2.0 with no extensions).  Increase the number if the shader fails to compile (not enough instructions or interpolators)."
dd_end
dd_start	lbl="OPTIONS"
---
subh	lbl="Shadows"
flag	lbl="Add Shadow/Depth Pass"		kw=addshadow			block="pragma_surface_shader"		tt="Force the shader to have the Shadow Caster pass.  Can help if shadows don't work properly with the shader.  Note that this pass is also used to render the object on the depth texture."
flag	lbl="Full Forward Shadows"		kw=fullforwardshadows	block="pragma_surface_shader"		tt="Enable support for all shadow types in Forward rendering path"
flag	lbl="Disable Shadow Receiving"	kw=noshadow				block="pragma_surface_shader"		tt="Disables all shadow receiving support in this shader"
space	space=4
subh	lbl="Misc"
sngl	lbl="Enable Fog"				kw=ENABLE_FOG												tt="Disables Unity Fog support.  Can help if you run out of vertex interpolators and don't need fog."
sngl	lbl="Enable Lightmaps"			kw=ENABLE_LIGHTMAPS											tt="Enable lightmap support (Dynamic/Baked GI support)"
sngl	lbl="Enable LPPV"				kw=ENABLE_LPPV												tt="Enable Light Probe Proxy Volume support"
sngl	lbl="Enable LOD Dither Crossfade"	kw=ENABLE_DITHER_LOD									tt="Enable Dither Crossfade support for transition between LOD levels"
flag	lbl="Disable Vertex Lighting"	kw=novertexlights		block="pragma_surface_shader"		tt="Disable vertex lights and spherical harmonics (light probes)"
sngl	lbl="Disable Dynamic Batching"	kw=DISABLE_BATCHING											tt="Disable dynamic batching support for this shader"
space	space=4
subh	lbl="Mobile-Friendly"
flag	lbl="One Directional Light"		kw=noforwardadd			block="pragma_surface_shader"		tt="Use additive lights as vertex lights, so that no additional lighting passes are used."
flag	lbl="Vertex View Dir"				kw=interpolateview	block="pragma_surface_shader"		tt="Calculate view direction per-vertex instead of per-pixel."	needsOr=SPECULAR,SPECULAR_ANISOTROPIC,SUBSURFACE,PARALLAX,RIM,RIM_OUTLINE
flag	lbl="Half as View"				kw=halfasview			block="pragma_surface_shader"		tt="Pass half-direction vector into the lighting function instead of view-direction.  Faster but inaccurate."		needsOr=SPECULAR,SPECULAR_ANISOTROPIC,SUBSURFACE,PARALLAX,RIM,RIM_OUTLINE
space	space=4
subh	lbl="GPU Instancing Options"
flag	lbl="Assume Uniform Scaling"	kw=assumeuniformscaling		block="pragma_gpu_instancing"		tt="Use this to instruct Unity to assume that all the instances have uniform scalings (the same scale for all X, Y and Z axes)."
flag	lbl="No LOD Fade"				kw=nolodfade				block="pragma_gpu_instancing"		tt="Use this to prevent Unity from applying GPU Instancing to LOD fade values."
flag	lbl="No Light Probe"			kw=nolightprobe				block="pragma_gpu_instancing"		tt="Use this to prevent Unity from applying GPU Instancing to Light Probe values (including their occlusion data). This is useful for performance if you are absolutely sure that there are no GameObjects using both GPU Instancing and Light Probes."
flag	lbl="No Lightmap"				kw=nolightmap				block="pragma_gpu_instancing"		tt="Use this to prevent Unity from applying GPU Instancing to Lightmap ST (atlas information) values. This is useful for performance if you are absolutely sure that there are no GameObjects using both GPU Instancing and lightmaps."
mult	lbl="Max Count"					kw=Off|,Define|GPU_INSTANCING_MAX_COUNT_DEFINE,Force|GPU_INSTANCING_MAX_COUNT_FORCE		tt="Use this to prevent Unity from applying GPU Instancing to Lightmap ST (atlas information) values. This is useful for performance if you are absolutely sure that there are no GameObjects using both GPU Instancing and lightmaps."
int		lbl="Max Count Value"			kw=GPU_INSTANCING_MAX_COUNT_VALUE	indent	needsOr=GPU_INSTANCING_MAX_COUNT_DEFINE,GPU_INSTANCING_MAX_COUNT_FORCE	default=50	min=1	max=2147483647
dd_end
dd_start	lbl="THIRD PARTY PLUGINS"
---
[[MODULE:FEATURES:VertExmotion]]
---
[[MODULE:FEATURES:CurvedWorld]]
#[[MODULE:FEATURES:Aura2]]
dd_end

#END

#================================================================

#PROPERTIES_NEW
header		Main Properties
/// IF !CUSTOM_ALBEDO
color_rgba	Albedo				fragment, imp(texture, label = "Albedo", variable = "_MainTex", drawer = "[MainTexture]", default = white, tiling_offset = true, global = true)
///
[[MODULE:PROPERTIES_NEW:AlbedoHSV]]
color_rgba	Main Color			fragment, imp(color, label = "Color", variable = "_Color", default = (1,1,1,1)), help = "An adjustable color multiplied with the final albedo color.  Set it to a white color constant if you don't plan on using it, to improve the shader performance."
/// IF !CUSTOM_ALBEDO
float		Alpha				fragment, imp(shader_property_ref, reference = Albedo, swizzle = A), imp(shader_property_ref, reference = Main Color, swizzle = A), help = "The output alpha value, generally only needed when using alpha blending transparency or alpha testing (cutout)",
///
/// IF ALPHA_TESTING
	/// IF ALPHA_TESTING_DITHERING
		/// IF ALPHA_DITHER_TEXTURE
float		Dithering Texture	fragment, imp(texture, label = "Alpha Dithering", variable = "_DitherTex", default = gray, uv_screenspace = true, scale_texel = true)
		///
	/// ELSE
float		Cutoff				fragment, imp(range, label = "Alpha Cutoff", default = 0.5, min = 0, max = 1), help = "The threshold value at which point pixels are discarded when using alpha testing (cutout)"
	///
///
float		Ambient Intensity	lighting, imp(constant, label = "Ambient Intensity", default = 1)

[[MODULE:PROPERTIES_NEW:Ramp Shading]]

color		Highlight Color		lighting, imp(color, label = "Highlight Color", variable = "_HColor", default = (0.75,0.75,0.75,1))
color		Shadow Color		lighting, imp(color, label = "Shadow Color", variable = "_SColor", default = (0.2,0.2,0.2,1))
[[MODULE:PROPERTIES_NEW:ShadowHSV]]

[[MODULE:PROPERTIES_NEW:Specular]]
[[MODULE:PROPERTIES_NEW:Rim Lighting]]
/// IF GLOSSY_REFLECTIONS || PLANAR_REFLECTION
	header		Reflections
	color		Reflection Color			lighting, imp(color, label = "Color", default = (1, 1, 1, 1))
	/// IF GLOSSY_REFLECTIONS
	float		Reflection Smoothness		lighting, imp(range, label = "Smoothness", default = 0.5, min = 0, max = 1)
	///
///
/// IF EMISSION
	header		Emission
	color		Emission									fragment, imp(color, label = "Emission Color", default = (0,0,0,1), hdr = true)
///
/// IF OCCLUSION
	header		Occlusion
	float		Occlusion									lighting, imp(shader_property_ref, reference = Albedo, swizzle = A)
///
[[MODULE:PROPERTIES_NEW:Subsurface Scattering]]
[[MODULE:PROPERTIES_NEW:Reflection]]
[[MODULE:PROPERTIES_NEW:MatCap]]
[[MODULE:PROPERTIES_NEW:Custom Ambient]]
[[MODULE:PROPERTIES_NEW:Terrain]]
[[MODULE:PROPERTIES_NEW:Vertex Displacement]]
[[MODULE:PROPERTIES_NEW:Normal Mapping]]
[[MODULE:PROPERTIES_NEW:Triplanar]]
[[MODULE:PROPERTIES_NEW:Texture Blending]]
[[MODULE:PROPERTIES_NEW:NdotL Stylization]]
[[MODULE:PROPERTIES_NEW:Sketch]]
[[MODULE:PROPERTIES_NEW:Outline]]
[[MODULE:PROPERTIES_NEW:Wind]]
[[MODULE:PROPERTIES_NEW:Water]]
[[MODULE:PROPERTIES_NEW:Depth Texture]]
[[MODULE:PROPERTIES_NEW:Dissolve]]
[[MODULE:PROPERTIES_NEW:Vertical Fog]]
/// IF PASS_SILHOUETTE
		header			Silhouette Pass
		color_rgba		Silhouette Color				lighting, imp(color, label = "Silhouette Color", default = (0,0,0,0.33))
	/// IF SILHOUETTE_STENCIL
		fixed_function_float	Silhouette Stencil Reference	fixed, imp(constant, label = "Silhouette Stencil Reference", default = 1)
	///
		fixed_function_enum		Silhouette Blend Source			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendFactor, default = "SrcAlpha")
		fixed_function_enum		Silhouette Blend Destination	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendFactor, default = "OneMinusSrcAlpha")
///
/// IF CUSTOM_BLENDING || BLEND_OP
header					Blending
///
/// IF CUSTOM_BLENDING
fixed_function_enum		Blend Source					fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendFactor, default = "SrcAlpha")
fixed_function_enum		Blend Destination				fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendFactor, default = "OneMinusSrcAlpha")
///
/// IF BLEND_OP
fixed_function_enum		Blend Operation					fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendOperation, default = "Add")
///
/// IF ZWRITE || ZTEST || CULLING
header		Shader States
///
/// IF ZWRITE
fixed_function_enum		Depth Write						fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.DepthWrite, default = "On")
///
/// IF ZTEST
fixed_function_enum		Depth Test						fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.CompareFunction, default = "LEqual")
///
/// IF CULLING
fixed_function_enum		Face Culling					fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.Culling, default = "Back")
///
/// IF STENCIL
		header	Stencil
		fixed_function_float		Stencil Reference	fixed, imp(constant, label = "Reference", default = 0)
		fixed_function_float		Stencil Read Mask	fixed, imp(constant, label = "Read Mask", default = 255)
		fixed_function_float		Stencil Write Mask	fixed, imp(constant, label = "Write Mask", default = 255)
	/// IF !STENCIL_DOUBLE_SIDED
		fixed_function_enum			Stencil Comparison	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.CompareFunction, default = "Always")
		fixed_function_enum			Stencil Pass		fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Fail		fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Depth Fail	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
	/// ELSE
		fixed_function_enum			Stencil Front Comparison	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.CompareFunction, default = "Always")
		fixed_function_enum			Stencil Front Pass			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Front Fail			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Front Depth Fail	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")

		fixed_function_enum			Stencil Back Comparison		fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.CompareFunction, default = "Always")
		fixed_function_enum			Stencil Back Pass			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Back Fail			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Back Depth Fail		fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
	///
///
header	Third Party
[[MODULE:PROPERTIES_NEW:VertExmotion]]
[[MODULE:PROPERTIES_NEW:CurvedWorld]]
[[MODULE:PROPERTIES_NEW:Aura2]]
header			Hooks					"Hooks are special Shader Properties that expose variables from the shader code that can then be freely modified"
float3			Vertex Position			vertex, label = "Vertex Position (Object Space)", imp(hook, label = "v.vertex.xyz", toggles = HOOK_VERTEX_POSITION), help = "The object-space vertex position, e.g. to make your own vertex displacement function.  Make sure to enable <b>'Add Shadow/Depth Pass'</b> in the <b>'Surface Shader Flags'</b> for the shadows to get affected too."
float3			Vertex Position World	vertex, label = "Vertex Position (World Space)", imp(hook, label = "worldPos.xyz", toggles = HOOK_VERTEX_POSITION_WORLD), help = "The world-space vertex position.  Make sure to enable <b>'Add Shadow/Depth Pass'</b> in the <b>'Surface Shader Flags'</b> for the shadows to get affected too."
float3			Main Light Direction			lighting, label = "Main Light Direction", imp(hook, label = "lightDir", toggles = HOOK_MAIN_LIGHT_DIR), help = "The direction of the main directional light."
float3			Additional Lights Direction		lighting, label = "Additional Lights Direction", imp(hook, label = "lightDir", toggles = HOOK_OTHER_LIGHTS_DIR), help = "The direction of additional lights."
color			Main Light Color				lighting, label = "Main Light Color", imp(hook, label = "lightColor", toggles = HOOK_MAIN_LIGHT_COLOR), help = "The color of the main directional light."
color			Additional Lights Color			lighting, label = "Additional Lights Color", imp(hook, label = "lightColor", toggles = HOOK_OTHER_LIGHTS_COLOR), help = "The color of additional lights."
color			Shading Ramp					lighting, label = "Shading Ramp", imp(hook, label = "ramp", toggles = HOOK_RAMP), help = "The colored ramp calculated, depending on the ramp settings, with the highlight and shadow colors."
color			Main Light Attenuation			lighting, label = "Main Light Attenuation", imp(hook, label = "atten", toggles = HOOK_MAIN_LIGHT_ATTEN), help = "The attenuation of the main directional light (shadow map)."
color			Additional Lights Attenuation	lighting, label = "Additional Lights Attenuation", imp(hook, label = "atten", toggles = HOOK_OTHER_LIGHTS_ATTEN), help = "The attenuation of additional lights (shadow map and distance attenuation)."
color			Final Albedo			fragment, imp(hook, label = "output.Albedo.rgb", toggles = HOOK_FINAL_ALBEDO), help = "The final albedo color used by the shader before lighting."
color			Final Ambient			fragment, imp(hook, label = "ambient.rgb", toggles = HOOK_FINAL_AMBIENT), help = "The final ambient color used by the shader, with any modifiers applied (e.g. Occlusion)"
color_rgba		Final Color				fragment, imp(hook, label = "color.rgba", toggles = HOOK_FINAL_COLOR), help = "The final color returned by the shader, after having processed all lighting and effects."
/// IF TERRAIN_SHADER
color_rgba		Terrain Splat Control	fragment, imp(hook, label = "terrain_splat_control.rgba", toggles = HOOK_SPLAT_CONTROL), help = "The terrain's internal splat control texture, before it is used to sample each layer."
///
/// IF TERRAIN_SHADER && TERRAIN_SHADER_8_LAYERS
color_rgba		Terrain Splat Control 1	fragment, imp(hook, label = "terrain_splat_control.rgba", toggles = HOOK_SPLAT_CONTROL_1), help = "The terrain's internal splat control 1 texture, before it is used to sample each layer."
///
header		Misc
/// IF USE_NDV_MIN_MAX_VERT
	float			NDV Min Vert		fragment, imp(range, label = "NDV Min (Vertex)", default = 0.5, min = 0, max = 2)
	float			NDV Max Vert		fragment, imp(range, label = "NDV Max (Vertex)", default = 1.0, min = 0, max = 2)
///
/// IF USE_NDV_MIN_MAX_FRAG
	float			NDV Min Frag		fragment, imp(range, label = "NDV Min", default = 0.5, min = 0, max = 2)
	float			NDV Max Frag		fragment, imp(range, label = "NDV Max", default = 1.0, min = 0, max = 2)
///
#END

#================================================================

#KEYWORDS

# flags
/// IF !ENABLE_LIGHTMAPS
	flag_on			nolightmap
/// ELSE
	flag_off		nolightmap
///
/// IF !ENABLE_FOG
	flag_on			nofog
/// ELSE
	flag_off		nofog
///
/// IF ENABLE_DITHER_LOD
	flag_on			dithercrossfade
/// ELSE
	flag_off		dithercrossfade
///
/// IF UNITY_5_6
	/// IF !ENABLE_LPPV
		flag_on		nolppv
	/// ELSE
		flag_off	nolppv
	///
///
/// IF SHADER_BLENDING || AUTO_TRANSPARENT_BLENDING
	flag_on			keepalpha
/// ELSE
	flag_off		keepalpha
///

/// IF GPU_INSTANCING_MAX_COUNT_DEFINE
	flag_on:pragma_gpu_instancing		maxcount
	flag_off:pragma_gpu_instancing		forcemaxcount
/// ELIF GPU_INSTANCING_MAX_COUNT_FORCE
	flag_on:pragma_gpu_instancing		forcemaxcount
	flag_off:pragma_gpu_instancing		maxcount
/// ELSE
	flag_off:pragma_gpu_instancing		forcemaxcount
	flag_off:pragma_gpu_instancing		maxcount
///

# blending
/// IF !AUTO_TRANSPARENT_BLENDING
	/// IF ALPHA_BLENDING
		flag_on		alpha:blend
	/// ELSE
		flag_off	alpha:blend
	///

	/// IF ALPHA_BLENDING_PREMULT
		flag_on		alpha:premul
	/// ELSE
		flag_off	alpha:premul
	///
///

# features
/// IF GLOSSY_REFLECTIONS
	feature_on		USE_VIEW_DIRECTION_FRAGMENT
///

/// IF ALPHA_TESTING && ALPHA_TESTING_DITHERING && !ALPHA_DITHER_TEXTURE
	feature_on		USE_DITHERING_FUNCTION
	feature_on		USE_SCREEN_POSITION_FRAGMENT
///

/// IF USE_NDV_VERTEX
	feature_on		USE_WORLD_NORMAL_VERTEX
	feature_on		USE_VIEW_DIRECTION_VERTEX
///

/// IF USE_NDV_FRAGMENT
	feature_on		USE_WORLD_NORMAL_FRAGMENT
	feature_on		USE_VIEW_DIRECTION_FRAGMENT
///

/// IF BACKFACE_LIGHTING_Z || BACKFACE_LIGHTING_XYZ
	feature_on		USE_VFACE
///

/// IF HOOK_VERTEX_POSITION_WORLD
	feature_on		APPLY_WORLD_POSITION
///

# queue
/// IF SHADER_BLENDING || OUTLINE_BLENDING
	feature_on		QUEUE_TRANSPARENT
///
/// IF ALPHA_TESTING
	feature_on		QUEUE_ALPHATEST
///

/// IF USE_NDV_FRAGMENT && USE_NDV_IGNORE_NORMAL_MAP && BUMP
	feature_on	USE_WORLD_NORMAL_FRAGMENT_PER_VERTEX
	feature_on	USE_VIEW_DIRECTION_FRAGMENT_PER_VERTEX
///

# rendertype
/// IF CURVED_WORLD
	/// IF ALPHA_TESTING
		set_keyword		RENDER_TYPE		CurvedWorld_TransparentCutout
	/// ELSE
		set_keyword		RENDER_TYPE		CurvedWorld_Opaque
	///
/// ELSE
	/// IF ALPHA_TESTING
		set_keyword		RENDER_TYPE		TransparentCutout
	/// ELSE
		set_keyword		RENDER_TYPE		Opaque
	///
///

# terrain
/// IF TERRAIN_SHADER
	feature_on		CUSTOM_ALBEDO
	flag_on			addshadow

	flag_on:pragma_gpu_instancing		assumeuniformscaling
	flag_on:pragma_gpu_instancing		nomatrices
	flag_on:pragma_gpu_instancing		nolightprobe
	flag_on:pragma_gpu_instancing		nolightmap
	flag_on:pragma_gpu_instancing		forwardadd

	/// IF TERRAIN_ADDPASS
	flag_off	alpha:blend
	flag_off	alpha:premul
	flag_on		decal:add
	flag_on		nometa
	///
///

[[MODULE:KEYWORDS]]

#END

#================================================================

/// IF TERRAIN_SHADER && TERRAIN_ADDPASS
// Terrain AddPass shader:
// This shader is used if your terrain uses more than 4 texture layers.
// It will draw the additional texture layers additively, by groups of 4 layers.

Shader "Hidden/@%SHADER_NAME%@-AddPass"
/// ELIF TERRAIN_SHADER && TERRAIN_BASEPASS
// Terrain BasePass shader:
// This shader is used when the terrain is viewed from the "Base Distance" setting.
// It uses low resolution generated textures from the "BaseGen" shader to draw the terrain entirely,
// thus preventing to perform the full splat map blending code to increase performances.

Shader "Hidden/@%SHADER_NAME%@-BasePass"
/// ELIF TERRAIN_SHADER && TERRAIN_BASEGEN
// Terrain BaseGen shader:
// This shader is used to generate full blended terrain maps at a low resolution, that will show if the camera is at the "Base Distance" setting of the terrain.
// This is a LOD-like system that prevents doing the full splat maps blending when the terrain is viewed from far away, and instead sample those generated maps only once.

Shader "Hidden/@%SHADER_NAME%@-BaseGen"
/// ELSE
Shader "@%SHADER_NAME%@"
///
{
/// IF !TERRAIN_BASEGEN
	Properties
	{
/// IF AUTO_TRANSPARENT_BLENDING
		[Enum(Front, 2, Back, 1, Both, 0)] _Cull ("Render Face", Float) = 2.0
		[TCP2ToggleNoKeyword] _ZWrite ("Depth Write", Float) = 1.0
		[HideInInspector] _RenderingMode ("rendering mode", Float) = 0.0
		[HideInInspector] _SrcBlend ("blending source", Float) = 1.0
		[HideInInspector] _DstBlend ("blending destination", Float) = 0.0
		[TCP2Separator]

///
		[[INJECTION_POINT:Properties/Start]]
		[TCP2HeaderHelp(Base)]
		[[PROP:Main Color]]
		[[PROP:Highlight Color]]
		[[PROP:Shadow Color]]
		[[MODULE:PROPERTIES_BLOCK:ShadowHSV]]
/// IF !CUSTOM_ALBEDO
		[[PROP:Albedo]]
		[[PROP:Alpha]]
///
		[[MODULE:PROPERTIES_BLOCK:AlbedoHSV]]
/// IF ALPHA_TESTING
	/// IF ALPHA_TESTING_DITHERING
		/// IF ALPHA_DITHER_TEXTURE
		[[PROP:Dithering Texture]]
		///
	/// ELSE
		[[PROP:Cutoff]]
	///
///
/// IF OCCLUSION
		[[PROP:Occlusion]]
///
		[TCP2Separator]

		[[MODULE:PROPERTIES_BLOCK:Ramp Shading]]
		[[MODULE:PROPERTIES_BLOCK:Terrain]]
		[[MODULE:PROPERTIES_BLOCK:Specular]]
/// IF EMISSION

		[TCP2HeaderHelp(Emission)]
		[[PROP:Emission]]
		[TCP2Separator]
///
		[[MODULE:PROPERTIES_BLOCK:Rim Lighting]]
/// IF (GLOSSY_REFLECTIONS || REFLECTION_CUBEMAP || PLANAR_REFLECTION)

		[TCP2HeaderHelp(Reflections)]
	/// IF REFLECTION_SHADER_FEATURE
		[Toggle(TCP2_REFLECTIONS)] _UseReflections ("Enable Reflections", Float) = 0
	///
///
/// IF GLOSSY_REFLECTIONS || PLANAR_REFLECTION
		[[PROP:Reflection Color]]
///
/// IF GLOSSY_REFLECTIONS
		[[PROP:Reflection Smoothness]]
///
		[[MODULE:PROPERTIES_BLOCK:Reflection]]
/// IF (GLOSSY_REFLECTIONS || REFLECTION_CUBEMAP || PLANAR_REFLECTION)
		[TCP2Separator]
///
		[[MODULE:PROPERTIES_BLOCK:Subsurface Scattering]]
		[[MODULE:PROPERTIES_BLOCK:MatCap]]
	#if_not_empty
		[TCP2HeaderHelp(Ambient Lighting)]
	#start_not_empty_block
/// IF AMBIENT_SHADER_FEATURE
		[Toggle(TCP2_AMBIENT)] _UseAmbient ("Enable Ambient/Indirect Diffuse", Float) = 0
///
		[[PROP:Ambient Intensity]]
		[[MODULE:PROPERTIES_BLOCK:Custom Ambient]]
	#end_not_empty_block
		[TCP2Separator]
	#end_not_empty
		[[MODULE:PROPERTIES_BLOCK:Vertex Displacement]]
		[[MODULE:PROPERTIES_BLOCK:Triplanar]]
		[[MODULE:PROPERTIES_BLOCK:Normal Mapping]]
		[[MODULE:PROPERTIES_BLOCK:Texture Blending]]
		[[MODULE:PROPERTIES_BLOCK:NdotL Stylization]]
		[[MODULE:PROPERTIES_BLOCK:Sketch]]
		[[MODULE:PROPERTIES_BLOCK:Wind]]
		[[MODULE:PROPERTIES_BLOCK:Water]]
		[[MODULE:PROPERTIES_BLOCK:Dissolve]]
		[[MODULE:PROPERTIES_BLOCK:Vertical Fog]]
/// IF PASS_SILHOUETTE
		[TCP2HeaderHelp(Silhouette Pass)]
		[[PROP:Silhouette Color]]
		[[PROP:Silhouette Blend Source]]
		[[PROP:Silhouette Blend Destination]]
		[TCP2Separator]
///
		[[MODULE:PROPERTIES_BLOCK:Outline]]
		[[MODULE:PROPERTIES_BLOCK:NoTile Sampling]]
		[[MODULE:PROPERTIES_BLOCK:Triplanar Sampling]]
/// IF USE_NDV_MIN_MAX_VERT
		[[PROP:NDV Min Vert]]
		[[PROP:NDV Max Vert]]
		[TCP2Separator]
///
/// IF USE_NDV_MIN_MAX_FRAG
		[[PROP:NDV Min Frag]]
		[[PROP:NDV Max Frag]]
		[TCP2Separator]
///
	#if_not_empty
	#start_not_empty_block
		[[PROP:Vertex Position]]
		[[PROP:Vertex Position World]]
		[[PROP:Final Color]]
	#end_not_empty_block
		[TCP2Separator]
	#end_not_empty
/// IF STENCIL
		[TCP2HeaderHelp(Stencil)]
		[[PROP:Stencil Reference]]
		[[PROP:Stencil Read Mask]]
		[[PROP:Stencil Write Mask]]
	/// IF !STENCIL_DOUBLE_SIDED
		[[PROP:Stencil Comparison]]
		[[PROP:Stencil Pass]]
		[[PROP:Stencil Fail]]
		[[PROP:Stencil Depth Fail]]
	/// ELSE
		[[PROP:Stencil Front Comparison]]
		[[PROP:Stencil Front Pass]]
		[[PROP:Stencil Front Fail]]
		[[PROP:Stencil Front Depth Fail]]
		[[PROP:Stencil Back Comparison]]
		[[PROP:Stencil Back Pass]]
		[[PROP:Stencil Back Fail]]
		[[PROP:Stencil Back Depth Fail]]
	///
		[TCP2Separator]
///
		[[PROPERTIES]]

		[[MODULE:PROPERTIES_BLOCK:CurvedWorld]]

/// IF CUSTOM_TIME && CUSTOM_TIME_LOCAL
		_CustomTime ("Custom Time", Vector) = (0.05, 1, 2, 3)
///
		[[INJECTION_POINT:Properties/End]]

		// Avoid compile error if the properties are ending with a drawer
		[HideInInspector] __dummy__ ("unused", Float) = 0
	}

	SubShader
	{
		Tags
		{
/// IF DISABLE_BATCHING
			"DisableBatching" = "True"
///
# Queues are ordered from highest to lowest in terms of priority
/// IF TERRAIN_SHADER
			"RenderType" = "Opaque"
	/// IF TERRAIN_ADDPASS
			"Queue"="Geometry-99"
			"IgnoreProjector"="True"
	/// ELSE
			"Queue"="Geometry-100"
	///
/// ELIF QUEUE_TRANSPARENT
			"RenderType"="Transparent"
			"Queue"="Transparent"
			"IgnoreProjectors"="True"
/// ELIF OUTLINE && OUTLINE_BEHIND_DEPTH
			"RenderType"="@%RENDER_TYPE%@"
			"Queue"="AlphaTest+25"
/// ELIF QUEUE_ALPHATEST
			"RenderType"="@%RENDER_TYPE%@"
			"Queue"="AlphaTest"
/// ELIF PASS_SILHOUETTE
			"RenderType"="@%RENDER_TYPE%@"
			"Queue"="Geometry+10" // Make sure that the objects are rendered later to avoid sorting issues with the transparent silhouette
/// ELSE
			"RenderType"="@%RENDER_TYPE%@"
///
/// IF TERRAIN_SHADER
			"TerrainCompatible"="True"
	/// IF TERRAIN_SHADER_8_LAYERS
			"SplatCount"="8"
	///
///
			[[INJECTION_POINT:SubShader/Tags]]
		}

		[[INJECTION_POINT:SubShader/Shader States]]

		CGINCLUDE

		#include "UnityCG.cginc"
		#include "UnityLightingCommon.cginc"	// needed for LightColor

/// IF UNITY_2019_4
		// Texture/Sampler abstraction
		#define TCP2_TEX2D_WITH_SAMPLER(tex)						UNITY_DECLARE_TEX2D(tex)
		#define TCP2_TEX2D_NO_SAMPLER(tex)							UNITY_DECLARE_TEX2D_NOSAMPLER(tex)
		#define TCP2_TEX2D_SAMPLE(tex, samplertex, coord)			UNITY_SAMPLE_TEX2D_SAMPLER(tex, samplertex, coord)
		#define TCP2_TEX2D_SAMPLE_LOD(tex, samplertex, coord, lod)	UNITY_SAMPLE_TEX2D_SAMPLER_LOD(tex, samplertex, coord, lod)
///

/// IF TERRAIN_SHADER
		// Terrain
	/// IF BUMP
		#define TERRAIN_INSTANCED_PERPIXEL_NORMAL
	///
	/// IF TERRAIN_ADDPASS
		#define TERRAIN_SPLAT_ADDPASS
	///
	/// IF TERRAIN_BASEPASS
		#define TERRAIN_BASE_PASS
	///

		[[MODULE:FUNCTIONS:Terrain]]
///

/// IF CUSTOM_TIME

		// Custom time variable overriding the built-in one
		#define _Time _CustomTime
///

		[[INJECTION_POINT:Include Files]]

/// IF CUSTOM_TIME
		float4 _CustomTime;
///
		[[VARIABLES_OUTSIDE_CBUFFER_INCLUDE]]
		[[MODULE:VARIABLES_OUTSIDE_CBUFFER]]
		[[VARIABLES_INCLUDE]]

		[[INJECTION_POINT:Variables/Outside CBuffer]]
		[[INJECTION_POINT:Variables/Inside CBuffer]]

		[[VARIABLES_GPU_INSTANCING_INCLUDE]]

		[[MODULE:VARIABLES]]

		[[MODULE:FUNCTIONS]]

		[[INJECTION_POINT:Functions]]

	#if_not_empty
		// Curved World 2020
	#start_not_empty_block
		[[MODULE:SHADER_FEATURES_BLOCK:CurvedWorld]]
	#end_not_empty_block
	#end_not_empty

		ENDCG

#PASS
/// IF OUTLINE
		// Outline Include
		CGINCLUDE

	/// IF ENABLE_FOG
		#pragma multi_compile_fog
	///
		[[INJECTION_POINT:Outline Pass/Pragma]]

		struct appdata_outline
		{
			float4 vertex : POSITION;
			float3 normal : NORMAL;
			[[VERTEX_INPUT_TEXCOORDS]]
			[[VERTEX_INPUT_OUTLINE]]
	/// IF USE_VERTEX_COLORS_VERT
			fixed4 vertexColor : COLOR;
	/// ELSE
		#if TCP2_COLORS_AS_NORMALS
			float4 vertexColor : COLOR;
		#endif
	///
	/// IF !USE_TANGENT_VERT && !USE_TANGENT_FRAGMENT && !VERTEXMOTION_NORMAL && !CURVED_WORLD_NORMAL
		#if TCP2_TANGENT_AS_NORMALS
	///
			float4 tangent : TANGENT;
	/// IF !USE_TANGENT_VERT && !USE_TANGENT_FRAGMENT && !VERTEXMOTION_NORMAL && !CURVED_WORLD_NORMAL
		#endif
	///
			[[INJECTION_POINT:Outline Pass/Attributes]]
			UNITY_VERTEX_INPUT_INSTANCE_ID
		};

		struct v2f_outline
		{
			float4 vertex : SV_POSITION;
	/// IF ENABLE_FOG
			UNITY_FOG_COORDS(0)
			[[INPUT_STRUCT_SEMANTICS:1]]
	/// ELSE
			[[INPUT_STRUCT_SEMANTICS:0]]
	///
			UNITY_VERTEX_OUTPUT_STEREO
#INPUT_VARIABLES
	/// IF USE_VERTEX_COLORS_FRAG
			fixed4 vertexColor;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT
			float4 screenPosition;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
			float3 worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
			float3 worldNormal;
	///
	/// IF USE_OBJECT_POSITION_FRAGMENT
			float3 objPos;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
			float3 objNormal;
	///
			[[MODULE:INPUT:Outline]]
#END
			[[INJECTION_POINT:Outline Pass/Varyings]]
		};

#INPUT = v
#OUTPUT = output
#VERTEX
		v2f_outline vertex_outline (appdata_outline v)
		{
			v2f_outline output;
			UNITY_INITIALIZE_OUTPUT(v2f_outline, output);
			UNITY_SETUP_INSTANCE_ID(v);
			UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

			[[MODULE:VERTEX:Terrain(v.vertex, v.normal, v.texcoord0, v.tangent)]]

			[[INJECTION_POINT:Outline Pass/Vertex Shader/Start]]

	/// IF USE_WORLD_POSITION_UV_VERTEX
			float3 worldPosUv = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
	/// IF USE_WORLD_NORMAL_UV_VERTEX || USE_WORLD_NORMAL_FRAGMENT
			float3 worldNormalUv = mul(unity_ObjectToWorld, float4(v.normal, 1.0)).xyz;
	///
			[[VERTEX_TEXCOORDS]]
			[[SAMPLE_CUSTOM_PROPERTIES]]
			[[SAMPLE_SHADER_PROPERTIES]]

			[[MODULE:VERTEX:VertExmotion(v.vertex, v.normal, v.tangent)]]
			[[MODULE:VERTEX:CurvedWorld(v.vertex, v.normal, v.tangent)]]

			[[MODULE:VERTEX:Vertex Displacement(v.vertex)]]
	/// IF HOOK_VERTEX_POSITION
			v.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
	///
	/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || USE_WORLD_POSITION_FRAGMENT || USE_WORLD_POSITION_VERTEX
			float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
			[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
	/// IF HOOK_VERTEX_POSITION_WORLD
			worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
	///
			[[MODULE:VERTEX:Wind(worldPos.xyz)]]
	/// IF APPLY_WORLD_POSITION
			v.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
	///
			[[MODULE:VERTEX:Water(v.vertex, worldPos, v.normal)]]
	/// IF USE_OBJECT_POSITION_FRAGMENT
			output.[[INPUT_VALUE:objPos]] = v.vertex.xyz;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
			output.[[INPUT_VALUE:objNormal]] = v.normal.xyz;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
			output.[[INPUT_VALUE:worldPos]] = worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
			output.[[INPUT_VALUE:worldNormal]] = worldNormalUv;
	///
	/// IF USE_VERTEX_COLORS_FRAG
			output.vertexColor = v.vertexColor;
	///
	/// IF USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX
			float4 clipPos = output.vertex;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

			// Screen Position
			float4 screenPos = ComputeScreenPos(clipPos);
		/// IF USE_SCREEN_POSITION_FRAGMENT
			output.screenPosition = screenPos;
		///
	///
			[[MODULE:VERTEX:Screen Space UV(screenPos, clipPos, output)]]
			[[MODULE:VERTEX:Depth Texture(output.screenPosition, clipPos)]]
		[[MODULE:VERTEX:Outline(v, output, null)]]
	/// IF ENABLE_FOG
#note: fog is handled in Module_Outline for the fragment part!
			UNITY_TRANSFER_FOG(output, output.vertex);
	///

			[[INJECTION_POINT:Outline Pass/Vertex Shader/End]]

			return output;
		}

#INPUT = input
#OUTPUT = no_output
#FRAGMENT
		float4 fragment_outline (v2f_outline input) : SV_Target
		{
			[[INJECTION_POINT:Outline Pass/Fragment Shader/Start]]

			[[MODULE:FRAGMENT:Screen Space UV(input.screenPosition, input)]]
			[[SAMPLE_CUSTOM_PROPERTIES]]
			[[SAMPLE_SHADER_PROPERTIES]]
			[[MODULE:FRAGMENT:Outline(input)]]

	/// IF ALPHA_TESTING
			// Alpha Testing
		/// IF ALPHA_TESTING_DITHERING
			/// IF ALPHA_DITHER_TEXTURE
			half cutoffValue = [[VALUE:Dithering Texture]];
			/// ELIF ALPHA_DITHER_8x8
			float2 ditherUV = (input.screenPosition.xy / input.screenPosition.w) * _ScreenParams.xy;
			half cutoffValue = Dither8x8(ditherUV.xy);
			/// ELSE
			float2 ditherUV = (input.screenPosition.xy / input.screenPosition.w) * _ScreenParams.xy;
			half cutoffValue = Dither4x4(ditherUV.xy);
			///
		/// ELSE
			half cutoffValue = [[VALUE:Cutoff]];
		///
			outlineColor.a *= [[VALUE:Alpha]];
		/// IF !ALPHA_TO_COVERAGE
			clip(outlineColor.a - cutoffValue);
		/// ELIF !ALPHA_TO_COVERAGE_RAW
			// Sharpen Alpha-to-Coverage
			outlineColor.a = (outlineColor.a - cutoffValue) / max(fwidth(outlineColor.a), 0.0001) + 0.5;
		///
	///

			[[MODULE:FRAGMENT:Dissolve(outlineColor.rgb)]]
			[[MODULE:FRAGMENT:Vertical Fog(outlineColor.rgb, input.[[INPUT_VALUE:worldPos]], input.[[INPUT_VALUE:objPos]])]]
	/// IF ENABLE_FOG
			UNITY_APPLY_FOG(input.fogCoord, outlineColor);
	///
			[[INJECTION_POINT:Outline Pass/Fragment Shader/End]]

			return outlineColor;
		}

		ENDCG
		// Outline Include End
///
/// IF OUTLINE && OUTLINE_BEHIND_DEPTH

		// Outline
		Pass
		{
			Name "Outline"
			Tags
			{
				"LightMode"="ForwardBase"
				[[INJECTION_POINT:Outline Pass/Tags]]
			}
			Cull Off
			ZWrite Off
	/// IF OUTLINE_ZSMOOTH
			Offset [[VALUE:Outline Offset Factor]],[[VALUE:Outline Offset Units]]
	///
	/// IF OUTLINE_BLENDING
			Blend [[VALUE:Outline Blend Source]] [[VALUE:Outline Blend Destination]]
	/// ELIF OUTLINE_OPAQUE
			Blend Off
	///
			[[INJECTION_POINT:Outline Pass/Shader States]]

			CGPROGRAM

			#pragma vertex vertex_outline
			#pragma fragment fragment_outline

			#pragma target @%SHADER_TARGET%@

			#pragma multi_compile _ TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV1_AS_NORMALS TCP2_UV2_AS_NORMALS TCP2_UV3_AS_NORMALS TCP2_UV4_AS_NORMALS
			#pragma multi_compile _ TCP2_UV_NORMALS_FULL TCP2_UV_NORMALS_ZW
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]
			
	#if_not_empty
			//--------------------------------------
			// Toony Colors Pro 2 keywords
	#start_not_empty_block
			[[MODULE:SHADER_FEATURES_BLOCK:Wind]]
			[[MODULE:SHADER_FEATURES_BLOCK:Dissolve]]
			[[MODULE:SHADER_FEATURES_BLOCK:Vertex Displacement]]
	#end_not_empty_block
	#end_not_empty

			ENDCG
		}
///
/// IF OUTLINE && OUTLINE_BEHIND_STENCIL

		Stencil
		{
			Ref [[VALUE:Outline Stencil Reference]]
			Comp Always
			Pass Replace
			[[INJECTION_POINT:Outline Pass/Stencil]]
		}
///
#PASS
/// IF DEPTH_PREPASS

		// Depth pre-pass
		Pass
		{
			Name "Depth Prepass"
			Tags
			{
				"LightMode"="ForwardBase"
				[[INJECTION_POINT:Depth Pre-Pass/Tags]]
			}
			ColorMask 0
			ZWrite On
			[[INJECTION_POINT:Depth Pre-Pass/Shader States]]

			CGPROGRAM
			#pragma vertex vertex_depthprepass
			#pragma fragment fragment_depthprepass
			#pragma target @%SHADER_TARGET%@
			[[INJECTION_POINT:Depth Pre-Pass/Pragma]]

			struct appdata_sil
			{
				float4 vertex : POSITION;
				[[VERTEX_INPUT_TEXCOORDS]]
	/// IF USE_VERTEX_COLORS_VERT
				fixed4 vertexColor : COLOR;
	///
				float3 normal : NORMAL;
	/// IF VERTEXMOTION_NORMAL || CURVED_WORLD_NORMAL
				float4 tangent : TANGENT;
	///
				[[INJECTION_POINT:Depth Pre-Pass/Attributes]]
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct v2f_depthprepass
			{
				float4 vertex : SV_POSITION;
				UNITY_VERTEX_OUTPUT_STEREO
				[[INPUT_STRUCT_SEMANTICS:0]]
#INPUT_VARIABLES
	/// IF USE_VERTEX_COLORS_FRAG
				fixed4 vertexColor;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT
				float4 screenPosition;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
				float3 worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormal;
	///
	/// IF USE_OBJECT_POSITION_FRAGMENT
				float3 objPos;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
				float3 objNormal;
	///
				[[MODULE:INPUT]]
#END
				[[INJECTION_POINT:Depth Pre-Pass/Varyings]]
			};

#INPUT = v
#OUTPUT = output
#VERTEX
			v2f_depthprepass vertex_depthprepass (appdata_sil v)
			{
				v2f_depthprepass output;
				UNITY_INITIALIZE_OUTPUT(v2f_depthprepass, output);
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

				[[INJECTION_POINT:Depth Pre-Pass/Vertex Shader/Start]]

/// IF USE_WORLD_POSITION_UV_VERTEX
				float3 worldPosUv = mul(unity_ObjectToWorld, v.vertex).xyz;
///
/// IF USE_WORLD_NORMAL_UV_VERTEX || USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormalUv = mul(unity_ObjectToWorld, float4(v.normal, 1.0)).xyz;
///
				[[VERTEX_TEXCOORDS]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[MODULE:VERTEX:VertExmotion(v.vertex, v.normal, v.tangent)]]
				[[MODULE:VERTEX:CurvedWorld(v.vertex, v.normal, v.tangent)]]

				[[MODULE:VERTEX:Vertex Displacement(v.vertex)]]
	/// IF HOOK_VERTEX_POSITION
				v.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
	///
	/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || USE_WORLD_POSITION_FRAGMENT || USE_WORLD_POSITION_VERTEX
				float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
				[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
	/// IF HOOK_VERTEX_POSITION_WORLD
				worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
	///
				[[MODULE:VERTEX:Wind(worldPos.xyz)]]
	/// IF APPLY_WORLD_POSITION
				v.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
	///
				[[MODULE:VERTEX:Water(v.vertex, worldPos, v.normal)]]
	/// IF USE_OBJECT_POSITION_FRAGMENT
				output.[[INPUT_VALUE:objPos]] = v.vertex.xyz;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:objNormal]] = v.normal.xyz;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
				output.[[INPUT_VALUE:worldPos]] = worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:worldNormal]] = worldNormalUv;
	///
				output.vertex = UnityObjectToClipPos(v.vertex);
	/// IF USE_VERTEX_COLORS_FRAG
				output.vertexColor = v.vertexColor;
	///
	/// IF USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX
				float4 clipPos = output.vertex;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

				// Screen Position
				float4 screenPos = ComputeScreenPos(clipPos);
		/// IF USE_SCREEN_POSITION_FRAGMENT
				output.screenPosition = screenPos;
		///
	///
				[[MODULE:VERTEX:Depth Texture(output.screenPosition, clipPos)]]
				[[MODULE:VERTEX:Screen Space UV(screenPos, clipPos, output)]]

				[[INJECTION_POINT:Depth Pre-Pass/Vertex Shader End]]

				return output;
			}

#INPUT = input
#OUTPUT = no_output
#FRAGMENT
			half4 fragment_depthprepass (v2f_depthprepass input) : SV_Target
			{
				[[INJECTION_POINT:Depth Pre-Pass/Fragment Shader/Start]]

				[[MODULE:FRAGMENT:Screen Space UV(input.screenPosition, input)]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[INJECTION_POINT:Depth Pre-Pass/Fragment Shader/End]]

				return 0;
			}
			ENDCG
		}
///
#PASS
/// IF PASS_SILHOUETTE
		// Silhouette Pass
		Pass
		{
			Name "Silhouette"
			Tags
			{
				"LightMode"="ForwardBase"
				[[INJECTION_POINT:Silhouette Pass/Tags]]
			}
			Blend [[VALUE:Silhouette Blend Source]] [[VALUE:Silhouette Blend Destination]]
			ZTest Greater
			ZWrite Off
			[[INJECTION_POINT:Silhouette Pass/Shader States]]
	/// IF SILHOUETTE_STENCIL

			Stencil
			{
				Ref [[VALUE:Silhouette Stencil Reference]]
				Comp NotEqual
				Pass Replace
				ReadMask [[VALUE:Silhouette Stencil Reference]]
				WriteMask [[VALUE:Silhouette Stencil Reference]]
				[[INJECTION_POINT:Silhouette Pass/Stencil]]
			}
	///

			CGPROGRAM
			#pragma vertex vertex_silhouette
			#pragma fragment fragment_silhouette
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]
			#pragma target @%SHADER_TARGET%@
			[[INJECTION_POINT:Silhouette Pass/Pragma]]

			struct appdata_sil
			{
				float4 vertex : POSITION;
				[[VERTEX_INPUT_TEXCOORDS]]
	/// IF USE_VERTEX_COLORS_VERT
				fixed4 vertexColor : COLOR;
	///
				float3 normal : NORMAL;
	/// IF VERTEXMOTION_NORMAL || CURVED_WORLD_NORMAL
				float4 tangent : TANGENT;
	///
				[[INJECTION_POINT:Silhouette Pass/Attributes]]
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct v2f_sil
			{
				float4 vertex : SV_POSITION;
				UNITY_VERTEX_OUTPUT_STEREO
				[[INPUT_STRUCT_SEMANTICS:0]]
#INPUT_VARIABLES
	/// IF USE_VERTEX_COLORS_FRAG
				fixed4 vertexColor;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT
				float4 screenPosition;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
				float3 worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormal;
	///
	/// IF USE_OBJECT_POSITION_FRAGMENT
				float3 objPos;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
				float3 objNormal;
	///
				[[MODULE:INPUT]]
#END
				[[INJECTION_POINT:Silhouette Pass/Varyings]]
			};

#INPUT = v
#OUTPUT = output
#VERTEX
			v2f_sil vertex_silhouette (appdata_sil v)
			{
				v2f_sil output;
				UNITY_INITIALIZE_OUTPUT(v2f_sil, output);
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

				[[INJECTION_POINT:Silhouette Pass/Vertex Shader/Start]]

/// IF USE_WORLD_POSITION_UV_VERTEX
				float3 worldPosUv = mul(unity_ObjectToWorld, v.vertex).xyz;
///
/// IF USE_WORLD_NORMAL_UV_VERTEX || USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormalUv = mul(unity_ObjectToWorld, float4(v.normal, 1.0)).xyz;
///
				[[VERTEX_TEXCOORDS]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[MODULE:VERTEX:VertExmotion(v.vertex, v.normal, v.tangent)]]
				[[MODULE:VERTEX:CurvedWorld(v.vertex, v.normal, v.tangent)]]

				[[MODULE:VERTEX:Vertex Displacement(v.vertex)]]
	/// IF HOOK_VERTEX_POSITION
				v.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
	///
	/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || USE_WORLD_POSITION_FRAGMENT || USE_WORLD_POSITION_VERTEX
				float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
				[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
	/// IF HOOK_VERTEX_POSITION_WORLD
				worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
	///
				[[MODULE:VERTEX:Wind(worldPos.xyz)]]
	/// IF APPLY_WORLD_POSITION
				v.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
	///
				[[MODULE:VERTEX:Water(v.vertex, worldPos, v.normal)]]
	/// IF USE_OBJECT_POSITION_FRAGMENT
				output.[[INPUT_VALUE:objPos]] = v.vertex.xyz;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:objNormal]] = v.normal.xyz;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
				output.[[INPUT_VALUE:worldPos]] = worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:worldNormal]] = worldNormalUv;
	///
				output.vertex = UnityObjectToClipPos(v.vertex);
	/// IF USE_VERTEX_COLORS_FRAG
				output.vertexColor = v.vertexColor;
	///
	/// IF USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX
				float4 clipPos = output.vertex;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

				// Screen Position
				float4 screenPos = ComputeScreenPos(clipPos);
		/// IF USE_SCREEN_POSITION_FRAGMENT
				output.screenPosition = screenPos;
		///
	///
				[[MODULE:VERTEX:Depth Texture(output.screenPosition, clipPos)]]
				[[MODULE:VERTEX:Screen Space UV(screenPos, clipPos, output)]]

				[[INJECTION_POINT:Silhouette Pass/Vertex Shader/End]]

				return output;
			}

#INPUT = input
#OUTPUT = no_output
#FRAGMENT
			half4 fragment_silhouette (v2f_sil input) : SV_Target
			{
				[[INJECTION_POINT:Silhouette Pass/Fragment Shader/Start]]

				[[MODULE:FRAGMENT:Screen Space UV(input.screenPosition, input)]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[INJECTION_POINT:Silhouette Pass/Fragment Shader/End]]

				return [[VALUE:Silhouette Color]];
			}
			ENDCG
		}

///
#PASS
		// Main Surface Shader
/// IF AUTO_TRANSPARENT_BLENDING
		Blend [_SrcBlend] [_DstBlend]
		Cull [_Cull]
		ZWrite [_ZWrite]
/// ELIF ALPHA_BLENDING
		Blend SrcAlpha OneMinusSrcAlpha
/// ELIF ALPHA_BLENDING_PREMULT
		Blend One OneMinusSrcAlpha
/// ELIF ADDITIVE_BLENDING
		Blend One One
/// ELIF MULTIPLICATIVE_BLENDING
		Blend DstColor Zero
/// ELIF CUSTOM_BLENDING
		Blend [[VALUE:Blend Source]] [[VALUE:Blend Destination]]
///
/// IF BLEND_OP
		BlendOp [[VALUE:Blend Operation]]
///
/// IF ALPHA_TESTING && ALPHA_TO_COVERAGE
		AlphaToMask On
///
/// IF ZWRITE && !AUTO_TRANSPARENT_BLENDING
		ZWrite [[VALUE:Depth Write]]
///
/// IF ZTEST
		ZTest [[VALUE:Depth Test]]
///
/// IF CULLING && !AUTO_TRANSPARENT_BLENDING
		Cull [[VALUE:Face Culling]]
///
		[[INJECTION_POINT:Main Pass/Shader States]]
/// IF STENCIL

		Stencil
		{
			Ref [[VALUE:Stencil Reference]]
			ReadMask [[VALUE:Stencil Read Mask]]
			WriteMask [[VALUE:Stencil Write Mask]]
	/// IF !STENCIL_DOUBLE_SIDED
			Comp [[VALUE:Stencil Comparison]]
			Pass [[VALUE:Stencil Pass]]
			Fail [[VALUE:Stencil Fail]]
			ZFail [[VALUE:Stencil Depth Fail]]
	/// ELSE
			CompFront [[VALUE:Stencil Front Comparison]]
			PassFront [[VALUE:Stencil Front Pass]]
			FailFront [[VALUE:Stencil Front Fail]]
			ZFailFront [[VALUE:Stencil Front Depth Fail]]

			CompBack [[VALUE:Stencil Back Comparison]]
			PassBack [[VALUE:Stencil Back Pass]]
			FailBack [[VALUE:Stencil Back Fail]]
			ZFailBack [[VALUE:Stencil Back Depth Fail]]
	///
			[[INJECTION_POINT:Main Pass/Stencil]]
		}
///

		CGPROGRAM

		#pragma surface surf ToonyColorsCustom vertex:vertex_surface exclude_path:deferred exclude_path:prepass keepalpha @%FLAGS:pragma_surface_shader%@
		[[GPU_INSTANCING_OPTIONS]]
		#pragma target @%SHADER_TARGET%@
		[[INJECTION_POINT:Main Pass/Pragma]]

	#if_not_empty
		//================================================================
		// SHADER KEYWORDS

	#start_not_empty_block
		[[MODULE:SHADER_FEATURES_BLOCK:Terrain]]
		[[MODULE:SHADER_FEATURES_BLOCK:Specular]]
		[[MODULE:SHADER_FEATURES_BLOCK:Vertex Displacement]]
		[[MODULE:SHADER_FEATURES_BLOCK:Rim Lighting]]
/// IF (GLOSSY_REFLECTIONS || REFLECTION_CUBEMAP || PLANAR_REFLECTION) && REFLECTION_SHADER_FEATURE
		#pragma shader_feature_local_fragment TCP2_REFLECTIONS
///
/// IF AMBIENT_SHADER_FEATURE
		#pragma shader_feature_local_fragment TCP2_AMBIENT
///
/// IF AUTO_TRANSPARENT_BLENDING
		#pragma shader_feature_local _ _ALPHABLEND_ON _ALPHAPREMULTIPLY_ON
///
		[[MODULE:SHADER_FEATURES_BLOCK:MatCap]]
		[[MODULE:SHADER_FEATURES_BLOCK:Subsurface Scattering]]
		[[MODULE:SHADER_FEATURES_BLOCK:Normal Mapping]]
		[[MODULE:SHADER_FEATURES_BLOCK:Sketch]]
		[[MODULE:SHADER_FEATURES_BLOCK:NdotL Stylization]]
		[[MODULE:SHADER_FEATURES_BLOCK:Wind]]
		[[MODULE:SHADER_FEATURES_BLOCK:Water]]
		[[MODULE:SHADER_FEATURES_BLOCK:Dissolve]]
		[[MODULE:SHADER_FEATURES_BLOCK:Vertical Fog]]
		[[MODULE:SHADER_FEATURES_BLOCK:Aura2]]
	#end_not_empty_block
	#end_not_empty

		//================================================================
		// STRUCTS

		// Vertex input
		struct appdata_tcp2
		{
			float4 vertex : POSITION;
			float3 normal : NORMAL;
			[[VERTEX_INPUT_TEXCOORDS]]
/// IF USE_TANGENT_VERT || USE_TANGENT_FRAGMENT || TERRAIN_SHADER
			half4 tangent : TANGENT;
/// ELSE
		#if defined(LIGHTMAP_ON) && defined(DIRLIGHTMAP_COMBINED)
			half4 tangent : TANGENT;
		#endif
///
/// IF USE_VERTEX_COLORS_VERT
			fixed4 vertexColor : COLOR;
///
			[[INJECTION_POINT:Main Pass/Attributes]]
			UNITY_VERTEX_INPUT_INSTANCE_ID
		};

		struct Input
		{
/// IF USE_VERTEX_COLORS_FRAG
			fixed4 vertexColor;
///
/// IF USE_VIEW_DIRECTION_FRAGMENT
			half3 viewDir;
	/// IF USE_VIEW_DIRECTION_FRAGMENT_PER_VERTEX
			half3 viewDirVertex;
	///
///
/// IF USE_TANGENT_FRAGMENT
			half3 tangent;
///
/// IF USE_WORLD_POSITION_FRAGMENT
			float3 worldPos;
///
/// IF USE_OBJECT_POSITION_FRAGMENT
			float3 objPos;
///
/// IF USE_OBJECT_NORMAL_FRAGMENT
			float3 objNormal;
///
/// IF USE_WORLD_NORMAL_FRAGMENT
			half3 worldNormal; INTERNAL_DATA
///
/// IF USE_WORLD_NORMAL_FRAGMENT_PER_VERTEX
			half3 worldNormalVertex;
///
/// IF USE_SCREEN_POSITION_FRAGMENT
			float4 screenPosition;
///
/// IF UV_SINE_ANIMATION_FRAGMENT
			float2 sinUvAnimVertexPos;
///
/// IF UV_SINE_ANIMATION_FRAGMENT_WORLD
			float2 sinUvAnimVertexWorldPos;
///
/// IF USE_VFACE
			float vFace : VFACE;
///
			[[MODULE:INPUT]]
			[[INPUT_STRUCT]]
			[[INJECTION_POINT:Main Pass/Surface Input]]
		};

		//================================================================

/// IF TERRAIN_SHADER && USE_TERRAIN_DATA_LIGHTING
		// Terrain data to pass to lighting function
		/*
		struct TerrainData
		{
			half4 splatControl;
			half4 specularColor;
			half3 mixedDiffuse;
			half smoothness;
			half metallic;
	/// IF USE_TERRAIN_MASKS
			half4 mask;
	///
		};
		*/
///

		// Custom SurfaceOutput
		struct SurfaceOutputCustom
		{
			half atten;
			half3 Albedo;
			half3 Normal;
/// IF USE_SURFACE_CUSTOM_NORMAL
			half4 NormalCustom;
///
/// IF USE_WORLD_NORMAL_FRAGMENT
			half3 worldNormal;
///
			half3 Emission;
			half Specular;
			half Gloss;
			half Alpha;
/// IF USE_NDV_FRAGMENT
			half ndv;
			half ndvRaw;
///
/// IF USE_NORMAL_TANGENT_SPACE_LIGHTING
			float3 normalTS;
///

			Input input;
/// IF TERRAIN_SHADER

	/// IF USE_TERRAIN_DATA_LIGHTING
			TerrainData terrainData;
	///
			half terrainWeight;
	/// IF TERRAIN_SHADER_8_LAYERS
			half terrainWeight1;
	///
///

			[[VARIABLES_SURFACE_OUTPUT]]
		};

		//================================================================
		// VERTEX FUNCTION

#VERTEX, INPUT = v, OUTPUT = output
		void vertex_surface(inout appdata_tcp2 v, out Input output)
		{
			UNITY_INITIALIZE_OUTPUT(Input, output);

			[[INJECTION_POINT:Main Pass/Vertex Shader/Start]]

			[[MODULE:VERTEX:Terrain(v.vertex, v.normal, v.texcoord0, v.tangent)]]

/// IF USE_WORLD_POSITION_UV_VERTEX
			float3 worldPosUv = mul(unity_ObjectToWorld, v.vertex).xyz;
///
/// IF USE_WORLD_NORMAL_UV_VERTEX
			float3 worldNormalUv = mul(unity_ObjectToWorld, float4(v.normal, 1.0)).xyz;
///
/// IF UV_SINE_ANIMATION_VERTEX

			// Used for texture UV sine animation
			float2 sinUvAnimVertexPos = v.vertex.xy + v.vertex.yz;
	/// IF UV_SINE_ANIMATION_FRAGMENT
			output.sinUvAnimVertexPos = sinUvAnimVertexPos;
	///
///
/// IF UV_SINE_ANIMATION_VERTEX_WORLD

			// Used for texture UV sine animation (world space)
			float2 sinUvAnimVertexWorldPos = worldPosUv.xy + worldPosUv.yz;
	/// IF UV_SINE_ANIMATION_FRAGMENT_WORLD
			output.sinUvAnimVertexWorldPos = sinUvAnimVertexWorldPos;
	///
///

			[[VERTEX_TEXCOORDS]]
			[[SAMPLE_CUSTOM_PROPERTIES]]
			[[SAMPLE_SHADER_PROPERTIES]]

			[[MODULE:VERTEX:VertExmotion(v.vertex, v.normal, v.tangent)]]
			[[MODULE:VERTEX:CurvedWorld(v.vertex, v.normal, v.tangent)]]
			[[MODULE:VERTEX:Aura2(v.vertex, output)]]

			[[MODULE:VERTEX:Vertex Displacement(v.vertex)]]
/// IF HOOK_VERTEX_POSITION
			v.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
///
/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || (USE_VIEW_DIRECTION_VERTEX || USE_VIEW_DIRECTION_FRAGMENT_PER_VERTEX) ||  USE_WORLD_POSITION_VERTEX
			float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
///
			[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
/// IF HOOK_VERTEX_POSITION_WORLD
			worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
///
			[[MODULE:VERTEX:Wind(worldPos.xyz)]]
/// IF APPLY_WORLD_POSITION
			v.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
///
			[[MODULE:VERTEX:Water(v.vertex, worldPos, v.normal)]]
/// IF USE_OBJECT_POSITION_FRAGMENT
			output.objPos = v.vertex.xyz;
///
/// IF USE_OBJECT_NORMAL_FRAGMENT
			output.objNormal = v.normal.xyz;
///
/// IF USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX
			float4 clipPos = UnityObjectToClipPos(v.vertex);
///
/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

			// Screen Position
			float4 screenPos = ComputeScreenPos(clipPos);
	/// IF USE_SCREEN_POSITION_FRAGMENT
			output.screenPosition = screenPos;
	///
///
			[[MODULE:VERTEX:Depth Texture(output.screenPosition, clipPos)]]
			[[MODULE:VERTEX:Screen Space UV(screenPos, clipPos, output)]]
/// IF USE_VERTEX_COLORS_FRAG
			output.vertexColor = v.vertexColor;
///
/// IF USE_VIEW_DIRECTION_VERTEX || USE_VIEW_DIRECTION_FRAGMENT_PER_VERTEX
			half3 viewDir = normalize(UnityWorldSpaceViewDir(worldPos));
	/// IF USE_VIEW_DIRECTION_FRAGMENT_PER_VERTEX
			output.viewDirVertex = viewDir;
	///
///
/// IF USE_WORLD_NORMAL_VERTEX || USE_WORLD_NORMAL_FRAGMENT_PER_VERTEX
			half3 worldNormal = UnityObjectToWorldNormal(v.normal);
///
#ENABLE_IMPL: float ndv, lbl = "Special/N·V (Vertex)", help = "The dot product between the normal and view direction.", toggles = "USE_NDV_VERTEX", options = "(Use Min/Max Properties,USE_NDV_MIN_MAX_VERT,config),(Invert,USE_NDV_INVERT_VERT)"
/// IF USE_NDV_VERTEX
			half ndv = abs(dot(viewDir, worldNormal));
			half ndvRaw = ndv;
	/// IF USE_NDV_INVERT_VERT
			ndv = 1 - ndv;
	///
	/// IF USE_NDV_MIN_MAX_VERT
			ndv = smoothstep([[VALUE:NDV Min Vert]], [[VALUE:NDV Max Vert]], ndv);
	///
///

/// IF USE_TANGENT_FRAGMENT
			output.tangent = mul(unity_ObjectToWorld, float4(v.tangent.xyz, 0)).xyz;
///
/// IF USE_WORLD_NORMAL_FRAGMENT_PER_VERTEX
			output.worldNormalVertex = worldNormal;
///
			[[MODULE:VERTEX:Rim Lighting(ndvRaw, viewDir, v.normal, output)]]
			[[MODULE:VERTEX:MatCap(v.normal, screenPos, output)]]

			[[INJECTION_POINT:Main Pass/Vertex Shader/End]]
#DISABLE_IMPL_ALL
		}

		//================================================================
		// SURFACE FUNCTION

#FRAGMENT, INPUT = input, OUTPUT = output
		void surf(Input input, inout SurfaceOutputCustom output)
		{
			[[INJECTION_POINT:Main Pass/Surface Function/Start]]
			[[FRAGMENT_TEXCOORDS]]
/// IF BUMP && USE_WORLD_NORMAL_FRAGMENT

			input.worldNormal = WorldNormalVector(input, output.Normal);

///
#ENABLE_IMPL: float input.vFace, lbl = "Special/VFACE (Face direction)", help = "Indicates if the current face is back or front-facing. Should be used with custom Face Culling.", toggles = "USE_VFACE", custom_code_compatible = true
/// IF BUMP && (PARALLAX || WORLD_NORMAL_FROM_BUMP)
	/// IF BUMP_SHADER_FEATURE
			#if defined(_NORMALMAP)
	///
			[[MODULE:FRAGMENT:Normal Mapping:PARALLAX_SURFACE(input.viewDir, input.texcoord0)]]
	/// IF WORLD_NORMAL_FROM_BUMP
			[[MODULE:FRAGMENT:Normal Mapping:BUMP_SAMPLE_BEFORE(output.Normal)]]
		/// IF USE_WORLD_NORMAL_FRAGMENT
			input.worldNormal = WorldNormalVector(input, output.Normal);
		///
	///
	/// IF BUMP_SHADER_FEATURE
			#endif

	///
///
			[[MODULE:FRAGMENT:Screen Space UV(input.screenPosition, input)]]
			[[SAMPLE_CUSTOM_PROPERTIES]]
			[[SAMPLE_SHADER_PROPERTIES]]
			output.input = input;

			[[MODULE:FRAGMENT:Terrain:PREPASS_AND_NORMAL(output.Normal, output)]]

			[[MODULE:FRAGMENT:Texture Blending:INIT_UVS]]
/// IF BUMP
	/// IF BUMP_SHADER_FEATURE
			#if defined(_NORMALMAP)
	///
			[[MODULE:FRAGMENT:Normal Mapping:BUMP_SAMPLE()]]
			[[MODULE:FRAGMENT:Texture Blending:BUMP()]]
			[[MODULE:FRAGMENT:Normal Mapping:UNPACK_BUMP_SURFACE(output.Normal, output.normalTS)]]

	/// IF BUMP_SHADER_FEATURE
			#endif

	///
///
/// IF USE_WORLD_NORMAL_FRAGMENT
			half3 worldNormal = WorldNormalVector(input, output.Normal);
			output.worldNormal = worldNormal;
///

#ENABLE_IMPL: float ndv, lbl = "Special/N·V", help = "The dot product between the normal and view direction.", toggles = "USE_NDV_FRAGMENT", options = "(Use Min/Max Properties,USE_NDV_MIN_MAX_FRAG,config),(Invert,USE_NDV_INVERT_FRAG),(Ignore Normal Map,USE_NDV_IGNORE_NORMAL_MAP)"
/// IF USE_NDV_FRAGMENT
	/// IF USE_NDV_IGNORE_NORMAL_MAP && BUMP
			half ndv = abs(dot(input.viewDirVertex, input.worldNormalVertex.xyz));
	/// ELSE
			half ndv = abs(dot(input.viewDir, normalize(output.Normal.xyz)));
	///
			half ndvRaw = ndv;
	/// IF USE_NDV_INVERT_FRAG
			ndv = 1 - ndv;
	///
	/// IF USE_NDV_MIN_MAX_FRAG
			ndv = smoothstep([[VALUE:NDV Min Frag]], [[VALUE:NDV Max Frag]], ndv);
	///
			output.ndv = ndv;
			output.ndvRaw = ndvRaw;

///
			[[MODULE:FRAGMENT:Depth Texture(input.screenPosition, positionNDC)]]

/// IF CUSTOM_ALBEDO
			output.Albedo = half3(1,1,1);
			output.Alpha = 1;
/// ELSE
			output.Albedo = [[VALUE:Albedo]].rgb;
			output.Alpha = [[VALUE:Alpha]];
///

			[[MODULE:FRAGMENT:Terrain(output.Albedo, output.Alpha, output.Normal, output)]]

			[[MODULE:FRAGMENT:Dissolve(output.Emission)]]
/// IF ALPHA_TESTING
			// Alpha Testing
	/// IF ALPHA_TESTING_DITHERING
		/// IF ALPHA_DITHER_TEXTURE
			half cutoffValue = [[VALUE:Dithering Texture]];
		/// ELIF ALPHA_DITHER_8x8
			float2 ditherUV = (input.screenPosition.xy / input.screenPosition.w) * _ScreenParams.xy;
			half cutoffValue = Dither8x8(ditherUV.xy);
		/// ELSE
			float2 ditherUV = (input.screenPosition.xy / input.screenPosition.w) * _ScreenParams.xy;
			half cutoffValue = Dither4x4(ditherUV.xy);
		///
	/// ELSE
			half cutoffValue = [[VALUE:Cutoff]];
	///
	/// IF ALPHA_TO_COVERAGE || ALPHA_TO_COVERAGE_RAW
			#if defined(SHADOWS_DEPTH)
				clip(output.Alpha - cutoffValue);
			#endif
	///
	/// IF !ALPHA_TO_COVERAGE
			clip(output.Alpha - cutoffValue);
	/// ELIF !ALPHA_TO_COVERAGE_RAW
			// Sharpen Alpha-to-Coverage
			output.Alpha = (output.Alpha - cutoffValue) / max(fwidth(output.Alpha), 0.0001) + 0.5;
	///
///
/// IF TEXTURE_BLENDING || TRIPLANAR
			half4 albedoAlpha = half4(output.Albedo, output.Alpha);
			[[MODULE:FRAGMENT:Triplanar:INPUT(input.worldPos, input.worldNormalVertex, input.objPos, input.objNormal)]]
			[[MODULE:FRAGMENT:Triplanar:SAMPLE_GROUND(albedoAlpha, input.objPos)]]
			[[MODULE:FRAGMENT:Texture Blending(albedoAlpha)]]
			[[MODULE:FRAGMENT:Triplanar(albedoAlpha, input.worldPos, input.objPos)]]
			output.Albedo = albedoAlpha.rgb;
///
			[[MODULE:FRAGMENT:AlbedoHSV(output.Albedo)]]
			output.Albedo *= [[VALUE:Main Color]].rgb;
			[[MODULE:FRAGMENT:Water(output.Albedo, output.Alpha)]]
/// IF HOOK_FINAL_ALBEDO
			output.Albedo.rgb = [[SAMPLE_VALUE_SHADER_PROPERTY:Final Albedo]];
///
/// IF EMISSION
			output.Emission += [[VALUE:Emission]];
///
			[[MODULE:FRAGMENT:Triplanar:BUMP(output.NormalCustom, input.worldNormalVertex)]]
			[[MODULE:FRAGMENT:MatCap(output.Albedo, output.Emission, worldNormal, input)]]

/// IF USE_NDV_FRAGMENT && USE_SURFACE_CUSTOM_NORMAL && !USE_NDV_IGNORE_NORMAL_MAP
			// Recalculate NDV to take the triplanar normals into account:
			ndv = abs(dot(input.viewDir, normalize(output.NormalCustom.xyz)));
			ndvRaw = ndv;
	/// IF USE_NDV_INVERT_FRAG
			ndv = 1 - ndv;
	///
	/// IF USE_NDV_MIN_MAX_FRAG
			ndv = smoothstep([[VALUE:NDV Min Frag]], [[VALUE:NDV Max Frag]], ndv);
	///
			output.ndv = ndv;
			output.ndvRaw = ndvRaw;
///

#DISABLE_IMPL_ALL
			[[INJECTION_POINT:Main Pass/Surface Function/End]]
		}

		//================================================================
		// LIGHTING FUNCTION

#LIGHTING, INPUT = surface, OUTPUT = _
/// IF USE_VIEW_DIRECTION_FRAGMENT
		inline half4 LightingToonyColorsCustom(inout SurfaceOutputCustom surface, half3 viewDir, UnityGI gi)
/// ELSE
		inline half4 LightingToonyColorsCustom(inout SurfaceOutputCustom surface, UnityGI gi)
///
		{
			[[INJECTION_POINT:Main Pass/Lighting Function/Start]]

[[MODULE:FRAGMENT:Terrain:SPECIAL]]

#ENABLE_IMPL: float3 viewDir, lbl = "Special/View Direction", help = "The world space view direction vector.", toggles = "USE_VIEW_DIRECTION_FRAGMENT"
#ENABLE_IMPL: float surface.input.vFace, lbl = "Special/VFACE (Face direction)", help = "Indicates if the current face is back or front-facing. Should be used with custom Face Culling.", toggles = "USE_VFACE", custom_code_compatible = false
#ENABLE_IMPL: float3 _LightColor0, lbl = "Special/Light Color", compat = "all", help = "The color of the current light used."
#ENABLE_IMPL: float ndv, lbl = "Special/N·V", help = "The dot product between the normal and view direction.", toggles = "USE_NDV_FRAGMENT", options = "(Use Min/Max Properties,USE_NDV_MIN_MAX_FRAG,config),(Invert,USE_NDV_INVERT_FRAG),(Ignore Normal Map,USE_NDV_IGNORE_NORMAL_MAP)"
/// IF USE_NDV_FRAGMENT
			half ndv = surface.ndv;
///
			half3 lightDir = gi.light.dir;
/// IF HOOK_MAIN_LIGHT_DIR
			#if !defined(UNITY_PASS_FORWARDADD)
				lightDir = normalize([[SAMPLE_VALUE_SHADER_PROPERTY:Main Light Direction]]);
			#endif
///
/// IF HOOK_OTHER_LIGHTS_DIR
			#if !defined(UNITY_PASS_FORWARDBASE)
				lightDir = normalize([[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Direction]]);
			#endif
///
			#if defined(UNITY_PASS_FORWARDBASE)
				half3 lightColor = _LightColor0.rgb;
				half atten = surface.atten;
			#else
				// extract attenuation from point/spot lights
				half3 lightColor = _LightColor0.rgb;
				half atten = max(gi.light.color.r, max(gi.light.color.g, gi.light.color.b)) / max(_LightColor0.r, max(_LightColor0.g, _LightColor0.b));
			#endif
/// IF HOOK_MAIN_LIGHT_COLOR
			#if !defined(UNITY_PASS_FORWARDADD)
				lightColor = [[SAMPLE_VALUE_SHADER_PROPERTY:Main Light Color]];
			#endif
///
/// IF HOOK_OTHER_LIGHTS_COLOR
			#if !defined(UNITY_PASS_FORWARDBASE)
				lightColor = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Color]];
			#endif
///
/// IF HOOK_MAIN_LIGHT_ATTEN
			#if !defined(UNITY_PASS_FORWARDADD)
				atten = [[SAMPLE_VALUE_SHADER_PROPERTY:Main Light Attenuation]];
			#endif
///
/// IF HOOK_OTHER_LIGHTS_ATTEN
			#if !defined(UNITY_PASS_FORWARDBASE)
				atten = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Attenuation]];
			#endif
///
#ENABLE_IMPL: float atten, lbl = "Special/Shadow Map", compat = "all", help = "The shadow map value for the current light."

/// IF USE_SURFACE_CUSTOM_NORMAL
	/// IF BUMP
			half3 normal = lerp(surface.NormalCustom.xyz, surface.Normal, surface.NormalCustom.w);
	/// ELSE
			half3 normal = surface.NormalCustom.xyz;
	///
/// ELSE
			half3 normal = normalize(surface.Normal);
///
/// IF BACKFACE_LIGHTING_Z
			normal.z *= (surface.input.vFace < 0) ? -1.0 : 1.0;
/// ELIF BACKFACE_LIGHTING_XYZ
			normal.xyz *= (surface.input.vFace < 0) ? -1.0 : 1.0;
///
			half ndl = dot(normal, lightDir);
#ENABLE_IMPL: float ndl, lbl = "Special/N·L", compat = "all", help = "The dot product between the normal and light direction."
			[[MODULE:FRAGMENT:NdotL Stylization:AFTER_NDL(ndl)]]
/// IF ATTEN_AT_NDL
			// Apply attenuation (shadowmaps & point/spot lights attenuation)
			ndl *= atten;
///
			half3 ramp;
			[[MODULE:FRAGMENT:Ramp Shading(ramp, ndl)]]
/// IF USE_NDL_GRAYSCALE
			half3 rampGrayscale = ramp;
///
#ENABLE_IMPL: float3 rampGrayscale, lbl = "Special/N·L Ramp (Black and White)", compat = "all", toggles = "USE_NDL_GRAYSCALE", help = "N·L with the ramp modification before the highlight/shadow colors are applied."

/// IF NO_RAMP_UNLIT
			#if !defined(UNITY_PASS_FORWARDBASE)
///
/// IF !ATTEN_AT_NDL
			// Apply attenuation (shadowmaps & point/spot lights attenuation)
			ramp *= atten;
///
			[[MODULE:FRAGMENT:ShadowHSV(surface.Albedo, ramp)]]
			[[MODULE:FRAGMENT:Sketch(ramp)]]
/// IF NO_RAMP_UNLIT
			#endif
///

			// Highlight/Shadow Colors
/// IF SHADOW_COLOR_LERP
			surface.Albedo = lerp([[VALUE:Shadow Color]], surface.Albedo, ramp);
			ramp = lerp(half3(1,1,1), [[VALUE:Highlight Color]], ramp);
/// ELSE
			#if !defined(UNITY_PASS_FORWARDBASE)
				ramp = lerp(half3(0,0,0), [[VALUE:Highlight Color]], ramp);
			#else
				ramp = lerp([[VALUE:Shadow Color]], [[VALUE:Highlight Color]], ramp);
			#endif
///

/// IF HOOK_RAMP
			ramp = [[SAMPLE_VALUE_SHADER_PROPERTY:Shading Ramp]];
///

#ENABLE_IMPL: float3 ramp, lbl = "Special/N·L Ramp (With Colors)", compat = "all", help = "N·L with the ramp modification with the highlight/shadow colors applied."
			[[MODULE:FRAGMENT:NdotL Stylization:AFTER_RAMP(ndl, ramp)]]

			// Output color
			half4 color;
/// IF NO_RAMP_UNLIT
			color.rgb = surface.Albedo * ramp;
/// ELSE
			color.rgb = surface.Albedo * lightColor.rgb * ramp;
///
			color.a = surface.Alpha;
			[[MODULE:FRAGMENT:Sketch:APPLY(color.rgb)]]

			// Apply indirect lighting (ambient)
/// IF OCCLUSION
			half occlusion = [[VALUE:Occlusion]];
/// ELSE
			half occlusion = 1;
///
			#ifdef UNITY_LIGHT_FUNCTION_APPLY_INDIRECT
/// IF AMBIENT_SHADER_FEATURE
			#if defined(TCP2_AMBIENT)
///
				half3 ambient = gi.indirect.diffuse;
				[[MODULE:FRAGMENT:Custom Ambient(ambient.rgb, normal)]]
				ambient *= surface.Albedo * occlusion * [[VALUE:Ambient Intensity]];

				[[MODULE:FRAGMENT:Sketch:APPLY_AMBIENT(ambient.rgb)]]
/// IF HOOK_FINAL_AMBIENT
				ambient.rgb = [[SAMPLE_VALUE_SHADER_PROPERTY:Final Ambient]];
///
				color.rgb += ambient;
/// IF AMBIENT_SHADER_FEATURE
			#endif
///
			#endif

			[[MODULE:FRAGMENT:Subsurface Scattering(color, normal, viewDir, surface.Albedo, lightColor, lightDir, atten)]]
/// IF AUTO_TRANSPARENT_BLENDING
			// Premultiply blending
			#if defined(_ALPHAPREMULTIPLY_ON)
				color.rgb *= color.a;
			#endif
///

			[[MODULE:FRAGMENT:Specular(color, normal, surface.input.tangent, lightDir, viewDir, ndl, surface.ndvRaw, atten, dummyLight)]]
			[[MODULE:FRAGMENT:Rim Lighting(surface.ndvRaw, color, color, normal, viewDir, surface.input.screenPosition, ndl, atten, surface.input)]]
	#if_not_empty
			// ForwardBase pass only
			#if !defined(UNITY_PASS_FORWARDADD)
	#start_not_empty_block
/// IF GLOSSY_REFLECTIONS

	/// IF GLOSSY_REFLECTIONS && REFLECTION_SHADER_FEATURE
				#if defined(TCP2_REFLECTIONS)
	///
					// Reflection probes/skybox
					half3 reflections = gi.indirect.specular * occlusion;
					[[MODULE:FRAGMENT:Reflection(reflections, surface.worldNormal, viewDir, surface.ndvRaw, surface.input.screenPosition, surface.normalTS)]]
					reflections *= [[VALUE:Reflection Color]];
					color.rgb += reflections;
	/// IF GLOSSY_REFLECTIONS && REFLECTION_SHADER_FEATURE
				#endif
	///
/// ELSE
	#if_not_empty

			half3 reflections = half3(0, 0, 0);
	#start_not_empty_block
			[[MODULE:FRAGMENT:Reflection(reflections, surface.worldNormal, viewDir, surface.ndvRaw, surface.input.screenPosition, surface.normalTS)]]
	#end_not_empty_block
	/// IF PLANAR_REFLECTION
			reflections *= [[VALUE:Reflection Color]];
	///
			color.rgb += reflections;
	#end_not_empty
///

				[[MODULE:FRAGMENT:Aura2(color.rgb, surface.input)]]
/// IF HOOK_FINAL_COLOR

			color.rgba = [[SAMPLE_VALUE_SHADER_PROPERTY:Final Color]];
///
	#end_not_empty_block

			#endif
	#end_not_empty
			[[MODULE:FRAGMENT:Vertical Fog(color.rgb, surface.input.worldPos, surface.input.objPos)]]
			[[INJECTION_POINT:Main Pass/Lighting Function/End]]
#DISABLE_IMPL_ALL

/// IF AUTO_TRANSPARENT_BLENDING
			// Apply alpha to Forward Add passes
			#if defined(_ALPHABLEND_ON) && defined(UNITY_PASS_FORWARDADD)
				color.rgb *= color.a;
			#endif
///
			[[MODULE:FRAGMENT:Terrain:FINAL_COLOR(surface.terrainWeight, surface.terrainWeight1, color)]]

			return color;
		}

/// IF ENABLE_LIGHTMAPS
		// Same as UnityGI_Base but with attenuation extraction that works with lightmaps
		inline UnityGI UnityGI_Base_TCP2(UnityGIInput data, half occlusion, half3 normalWorld, out half tcp2_atten)
		{
			UnityGI o_gi;
			ResetUnityGI(o_gi);

			// Base pass with Lightmap support is responsible for handling ShadowMask / blending here for performance reason
			#if defined(HANDLE_SHADOWS_BLENDING_IN_GI)
				half bakedAtten = UnitySampleBakedOcclusion(data.lightmapUV.xy, data.worldPos);
				float zDist = dot(_WorldSpaceCameraPos - data.worldPos, UNITY_MATRIX_V[2].xyz);
				float fadeDist = UnityComputeShadowFadeDistance(data.worldPos, zDist);
				data.atten = UnityMixRealtimeAndBakedShadows(data.atten, bakedAtten, UnityComputeShadowFade(fadeDist));
			#endif

			o_gi.light = data.light;

			// TCP2: don't apply attenuation to light color
			// o_gi.light.color *= data.atten;

			// TCP2: extract attenuation
			tcp2_atten = data.atten;

			#if UNITY_SHOULD_SAMPLE_SH
				o_gi.indirect.diffuse = ShadeSHPerPixel(normalWorld, data.ambient, data.worldPos);
			#endif

			#if defined(LIGHTMAP_ON)
				// Baked lightmaps
				half4 bakedColorTex = UNITY_SAMPLE_TEX2D(unity_Lightmap, data.lightmapUV.xy);
				half3 bakedColor = DecodeLightmap(bakedColorTex);

				#ifdef DIRLIGHTMAP_COMBINED
					fixed4 bakedDirTex = UNITY_SAMPLE_TEX2D_SAMPLER (unity_LightmapInd, unity_Lightmap, data.lightmapUV.xy);
					o_gi.indirect.diffuse += DecodeDirectionalLightmap (bakedColor, bakedDirTex, normalWorld);

					#if defined(LIGHTMAP_SHADOW_MIXING) && !defined(SHADOWS_SHADOWMASK) && defined(SHADOWS_SCREEN)
						ResetUnityLight(o_gi.light);
						o_gi.indirect.diffuse = SubtractMainLightWithRealtimeAttenuationFromLightmap (o_gi.indirect.diffuse, data.atten, bakedColorTex, normalWorld);
					#endif

				#else // not directional lightmap
					o_gi.indirect.diffuse += bakedColor;

					#if defined(LIGHTMAP_SHADOW_MIXING) && !defined(SHADOWS_SHADOWMASK) && defined(SHADOWS_SCREEN)
						ResetUnityLight(o_gi.light);
						o_gi.indirect.diffuse = SubtractMainLightWithRealtimeAttenuationFromLightmap(o_gi.indirect.diffuse, data.atten, bakedColorTex, normalWorld);
					#endif

				#endif
			#endif

			#ifdef DYNAMICLIGHTMAP_ON
				// Dynamic lightmaps
				fixed4 realtimeColorTex = UNITY_SAMPLE_TEX2D(unity_DynamicLightmap, data.lightmapUV.zw);
				half3 realtimeColor = DecodeRealtimeLightmap (realtimeColorTex);

				#ifdef DIRLIGHTMAP_COMBINED
					half4 realtimeDirTex = UNITY_SAMPLE_TEX2D_SAMPLER(unity_DynamicDirectionality, unity_DynamicLightmap, data.lightmapUV.zw);
					o_gi.indirect.diffuse += DecodeDirectionalLightmap (realtimeColor, realtimeDirTex, normalWorld);
				#else
					o_gi.indirect.diffuse += realtimeColor;
				#endif
			#endif

			o_gi.indirect.diffuse *= occlusion;
			return o_gi;
		}

		inline UnityGI UnityGlobalIllumination_TCP2 (UnityGIInput data, half occlusion, half3 normalWorld, out half tcp2_atten)
		{
			return UnityGI_Base_TCP2(data, occlusion, normalWorld, tcp2_atten);
		}

		inline UnityGI UnityGlobalIllumination_TCP2 (UnityGIInput data, half occlusion, half3 normalWorld, Unity_GlossyEnvironmentData glossIn, out half tcp2_atten)
		{
			UnityGI o_gi = UnityGI_Base_TCP2(data, occlusion, normalWorld, tcp2_atten);
			o_gi.indirect.specular = UnityGI_IndirectSpecular(data, occlusion, glossIn);
			return o_gi;
		}

///

		void LightingToonyColorsCustom_GI(inout SurfaceOutputCustom surface, UnityGIInput data, inout UnityGI gi)
		{
			[[INJECTION_POINT:Main Pass/GI Function/Start]]
/// IF USE_SURFACE_CUSTOM_NORMAL
	/// IF BUMP
			half3 normal = lerp(surface.NormalCustom.xyz, surface.Normal, surface.NormalCustom.w);
	/// ELSE
			half3 normal = surface.NormalCustom.xyz;
	///
/// ELSE
			half3 normal = surface.Normal;
///

/// IF GLOSSY_REFLECTIONS
	/// IF REFLECTION_SHADER_FEATURE
			#if defined(TCP2_REFLECTIONS)
	///
			// GI with reflection probes support
			half smoothness = [[VALUE:Reflection Smoothness]];
			Unity_GlossyEnvironmentData g = UnityGlossyEnvironmentSetup(smoothness, data.worldViewDir, normal, half3(0,0,0));	// last parameter is actually unused
	/// IF ENABLE_LIGHTMAPS
			half tcp2_atten;
			gi = UnityGlobalIllumination_TCP2(data, 1.0, normal, g, tcp2_atten); // occlusion is applied in the lighting function, if necessary
	/// ELSE
			gi = UnityGlobalIllumination(data, 1.0, normal, g); // occlusion is applied in the lighting function, if necessary
	///
	/// IF REFLECTION_SHADER_FEATURE
			#else
	/// IF ENABLE_LIGHTMAPS
			half tcp2_atten;
			gi = UnityGlobalIllumination_TCP2(data, 1.0, normal, tcp2_atten); // occlusion is applied in the lighting function, if necessary
	/// ELSE
			gi = UnityGlobalIllumination(data, 1.0, normal); // occlusion is applied in the lighting function, if necessary
	///
			#endif
	///
/// ELSE
			// GI without reflection probes
	/// IF ENABLE_LIGHTMAPS
			half tcp2_atten;
			gi = UnityGlobalIllumination_TCP2(data, 1.0, normal, tcp2_atten); // occlusion is applied in the lighting function, if necessary
	/// ELSE
			gi = UnityGlobalIllumination(data, 1.0, normal); // occlusion is applied in the lighting function, if necessary
	///
///

/// IF ENABLE_LIGHTMAPS
			surface.atten = tcp2_atten; // transfer attenuation to lighting function
/// ELSE
			surface.atten = data.atten; // transfer attenuation to lighting function
			gi.light.color = _LightColor0.rgb; // remove attenuation
///

			[[INJECTION_POINT:Main Pass/GI Function/End]]
		}

		ENDCG

/// IF OUTLINE && !OUTLINE_BEHIND_DEPTH
		// Outline
		Pass
		{
			Name "Outline"
			Tags
			{
				"LightMode"="ForwardBase"
				[[INJECTION_POINT:Outline Pass/Tags]]
			}
			Cull Front
	/// IF OUTLINE_ZSMOOTH
			Offset [[VALUE:Outline Offset Factor]],[[VALUE:Outline Offset Units]]
	///
	/// IF OUTLINE_BLENDING
			Blend [[VALUE:Outline Blend Source]] [[VALUE:Outline Blend Destination]]
	/// ELIF OUTLINE_OPAQUE
			Blend Off
	///
	/// IF ALPHA_TESTING_OUTLINE && ALPHA_TESTING && ALPHA_TO_COVERAGE
			AlphaToMask On
	///
			[[INJECTION_POINT:Outline Pass/Shader States]]
	/// IF OUTLINE_BEHIND_STENCIL
			Stencil
			{
				Ref [[VALUE:Outline Stencil Reference]]
				Comp NotEqual
				Pass Keep
				[[INJECTION_POINT:Outline Pass/Stencil]]
			}
	///

			CGPROGRAM
			#pragma vertex vertex_outline
			#pragma fragment fragment_outline
			#pragma target @%SHADER_TARGET%@
			#pragma multi_compile _ TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV1_AS_NORMALS TCP2_UV2_AS_NORMALS TCP2_UV3_AS_NORMALS TCP2_UV4_AS_NORMALS
			#pragma multi_compile _ TCP2_UV_NORMALS_FULL TCP2_UV_NORMALS_ZW
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]
			[[MODULE:SHADER_FEATURES_BLOCK:Dissolve]]
			[[MODULE:SHADER_FEATURES_BLOCK:Vertex Displacement]]
			ENDCG
		}
///
/// IF OUTLINE && OUTLINE_BEHIND_DEPTH && OUTLINE_DEPTH

		// Outline - Depth Pass Only
		Pass
		{
			Name "Outline Depth"
			Tags
			{
				"LightMode"="ForwardBase"
				[[INJECTION_POINT:Outline Depth Pass/Tags]]
			}
			Cull Off
	/// IF OUTLINE_ZSMOOTH
			Offset [[VALUE:Outline Offset Factor]],[[VALUE:Outline Offset Units]]
	///

			// Write to Depth Buffer only
			ColorMask 0
			ZWrite On

			[[INJECTION_POINT:Outline Depth Pass/Shader States]]

			CGPROGRAM
			#pragma vertex vertex_outline
			#pragma fragment fragment_outline
			#pragma multi_compile _ TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV1_AS_NORMALS TCP2_UV2_AS_NORMALS TCP2_UV3_AS_NORMALS TCP2_UV4_AS_NORMALS
			#pragma multi_compile _ TCP2_UV_NORMALS_FULL TCP2_UV_NORMALS_ZW
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]
			#pragma target @%SHADER_TARGET%@
			ENDCG
		}
///
#PASS
/// IF DITHERED_SHADOWS || (CUTOUT && CUTOUT_DITHER) || (OUTLINE && OUTLINE_SHADOWCASTER)
		//================================================================
		// SHADOW CASTER PASS

		// Shadow Caster (for shadows and depth texture)
		Pass
		{
			Name "ShadowCaster"
			Tags
			{
				"LightMode" = "ShadowCaster"
				[[INJECTION_POINT:Shadow Caster Pass/Tags]]
			}
			ZWrite On
			Blend Off

			[[INJECTION_POINT:Shadow Caster Pass/Shader States]]

			CGPROGRAM

			#define SHADOWCASTER_PASS

			#pragma vertex vertex_shadowcaster
			#pragma fragment fragment_shadowcaster
			#pragma multi_compile_shadowcaster
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]

	/// IF OUTLINE && OUTLINE_SHADOWCASTER
			#pragma multi_compile TCP2_NONE TCP2_ZSMOOTH_ON
			#pragma multi_compile TCP2_NONE TCP2_OUTLINE_CONST_SIZE
			#pragma multi_compile _ TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV1_AS_NORMALS TCP2_UV2_AS_NORMALS TCP2_UV3_AS_NORMALS TCP2_UV4_AS_NORMALS
			#pragma multi_compile _ TCP2_UV_NORMALS_FULL TCP2_UV_NORMALS_ZW
	///

		#if_not_empty
			// Curved World 2020
		#start_not_empty_block
			[[MODULE:SHADER_FEATURES_BLOCK:CurvedWorld]]
		#end_not_empty_block
		#end_not_empty

			[[MODULE:SHADER_FEATURES_BLOCK:Dissolve]]
			[[MODULE:SHADER_FEATURES_BLOCK:Vertex Displacement]]

			[[INJECTION_POINT:Shadow Caster Pass/Pragma]]

			// half _Cutoff;
	/// IF DITHERED_SHADOWS || (CUTOUT && CUTOUT_DITHER)
			sampler3D	_DitherMaskLOD;
	///

			struct appdata_shadowcaster
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
				[[VERTEX_INPUT_TEXCOORDS]]
	/// IF USE_VERTEX_COLORS_VERT
				fixed4 vertexColor : COLOR;
	/// ELSE
			#if TCP2_COLORS_AS_NORMALS
				float4 vertexColor : COLOR;
			#endif
	///
			// TODO: need a way to know if texcoord1 is used in the Shader Properties
			#if TCP2_UV2_AS_NORMALS
				float2 uv2 : TEXCOORD1;
			#endif
	/// IF !USE_TANGENT_VERT && !USE_TANGENT_FRAGMENT && !VERTEXMOTION_NORMAL && !CURVED_WORLD_NORMAL
			#if TCP2_TANGENT_AS_NORMALS
	///
				float4 tangent : TANGENT;
	/// IF !USE_TANGENT_VERT && !USE_TANGENT_FRAGMENT && !VERTEXMOTION_NORMAL && !CURVED_WORLD_NORMAL
			#endif
	///
				[[INJECTION_POINT:Shadow Caster Pass/Attributes]]
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct v2f_shadowcaster
			{
				V2F_SHADOW_CASTER_NOPOS
				[[INPUT_STRUCT_SEMANTICS:1]]
				UNITY_VERTEX_OUTPUT_STEREO
#INPUT_VARIABLES
	/// IF USE_VERTEX_COLORS_FRAG
				fixed4 vertexColor;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT
				float4 screenPosition;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
				float3 worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormal;
	///
	/// IF USE_OBJECT_POSITION_FRAGMENT
				float3 objPos;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
				float3 objNormal;
	///
				[[MODULE:INPUT:Outline]]
#END
				[[INJECTION_POINT:Shadow Caster Pass/Varyings]]
			};

#INPUT = v
#OUTPUT = output
#VERTEX
			void vertex_shadowcaster (appdata_shadowcaster v, out v2f_shadowcaster output, out float4 opos : SV_POSITION)
			{
				UNITY_INITIALIZE_OUTPUT(v2f_shadowcaster, output);
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

				[[INJECTION_POINT:Shadow Caster Pass/Vertex Shader/Start]]

	/// IF USE_WORLD_POSITION_UV_VERTEX
				float3 worldPosUv = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
	/// IF USE_WORLD_NORMAL_UV_VERTEX || USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormalUv = mul(unity_ObjectToWorld, float4(v.normal, 1.0)).xyz;
	///
				[[VERTEX_TEXCOORDS]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[MODULE:VERTEX:VertExmotion(v.vertex, v.normal, v.tangent)]]
				[[MODULE:VERTEX:CurvedWorld(v.vertex, v.normal, v.tangent)]]

				[[MODULE:VERTEX:Vertex Displacement(v.vertex)]]
	/// IF HOOK_VERTEX_POSITION
				v.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
	///
	/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || USE_WORLD_POSITION_FRAGMENT || USE_WORLD_POSITION_VERTEX
				float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
				[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
	/// IF HOOK_VERTEX_POSITION_WORLD
				worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
	///
				[[MODULE:VERTEX:Wind(worldPos.xyz)]]
	/// IF APPLY_WORLD_POSITION
				v.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
	///
				[[MODULE:VERTEX:Water(v.vertex, worldPos, v.normal)]]
	/// IF USE_OBJECT_POSITION_FRAGMENT
				output.[[INPUT_VALUE:objPos]] = v.vertex.xyz;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:objNormal]] = v.normal.xyz;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
				output.[[INPUT_VALUE:worldPos]] = worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:worldNormal]] = worldNormalUv;
	///
	/// IF USE_VERTEX_COLORS_FRAG
				output.vertexColor = v.vertexColor;
	///
				[[MODULE:VERTEX:Screen Space UV(screenPos, clipPos, output)]]
			[[MODULE:VERTEX:Outline(v, output, opos)]]
	/// IF USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX
				float4 clipPos = UnityObjectToClipPos(v.vertex);
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

				// Screen Position
				float4 screenPos = ComputeScreenPos(clipPos);
		/// IF USE_SCREEN_POSITION_FRAGMENT
				output.screenPosition = screenPos;
		///
	///
				[[MODULE:VERTEX:Depth Texture(output.screenPosition, clipPos)]]

				[[INJECTION_POINT:Shadow Caster Pass/Vertex Shader/End]]

				TRANSFER_SHADOW_CASTER_NOPOS(output,opos)
			}

#INPUT = input
#OUTPUT = _
#FRAGMENT
			half4 fragment_shadowcaster(v2f_shadowcaster input, UNITY_VPOS_TYPE vpos : VPOS) : SV_Target
			{
				[[INJECTION_POINT:Shadow Caster Pass/Fragment Shader/Start]]

				[[MODULE:FRAGMENT:Screen Space UV(input.screenPosition, input)]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]
	/// IF DITHERED_SHADOWS || (CUTOUT && CUTOUT_DITHER)
				// Use dither mask for alpha blended shadows, based on pixel position xy
				// and alpha level. Our dither texture is 4x4x16.
				half alpha = [[VALUE:Alpha]];
				half alphaRef = tex3D(_DitherMaskLOD, float3(vpos.xy*0.25,alpha*0.9375)).a;
				clip (alphaRef - 0.01);

	///
				[[INJECTION_POINT:Shadow Caster Pass/Fragment Shader/End]]

				SHADOW_CASTER_FRAGMENT(input)
			}

			ENDCG
		}
///
/// IF TERRAIN_SHADER
		UsePass "Hidden/Nature/Terrain/Utilities/PICKING"
		UsePass "Hidden/Nature/Terrain/Utilities/SELECTION"
///
	}

/// IF TERRAIN_SHADER && !TERRAIN_ADDPASS && !TERRAIN_BASEPASS && !TERRAIN_BASEGEN
	/// IF !TERRAIN_SHADER_8_LAYERS
	Dependency "AddPassShader"    = "Hidden/@%SHADER_NAME%@-AddPass"
	///
	Dependency "BaseMapShader"    = "Hidden/@%SHADER_NAME%@-BasePass"
	Dependency "BaseMapGenShader" = "Hidden/@%SHADER_NAME%@-BaseGen"
///

/// IF !USE_DEPTH_BUFFER
	Fallback "Diffuse"
///
	CustomEditor "ToonyColorsPro.ShaderGenerator.MaterialInspector_SG2"
/// ELSE
#IF TERRAIN_BASEGEN:
[[MODULE:TERRAIN_BASEGEN_SHADER:Terrain]]
///
}