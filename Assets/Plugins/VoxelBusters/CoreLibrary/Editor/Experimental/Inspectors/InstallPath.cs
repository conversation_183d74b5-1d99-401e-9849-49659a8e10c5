using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace VoxelBusters.CoreLibrary.Editor.Experimental
{
    public static class InstallPath
    {
        #region Static properties

        public static string EssentialKit { get; private set; } = "https://u3d.as/1szE";

        public static string ScreenRecorderKit { get; private set; } = "http://u3d.as/1nN3";

        public static string SocialKit { get; private set; } = "https://u3d.as/1pMn";

        public static string MLKit { get; private set; } = "https://u3d.as/2PMe";

        public static string ReportingKit { get; private set; } = "https://u3d.as/2Q6p";

        public static string AdsKit { get; private set; } = "https://u3d.as/37du";

        #endregion
    }
}