using System;
using System.Collections.Generic;
using b100SDK.Scripts.Ads.Admob;
using b100SDK.Scripts.Ads.Applovin;
using b100SDK.Scripts.Ads.Config;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Ads
{
    public class AdManager : BhSingletonPersistent<AdManager>
    {

        [PropertySpace(20f)]
        [SerializeField]
        private bool isDebugAd;
        
        
        private AdConfig _adConfig;
        
        private static Dictionary<AdProviderType, AdProvider> _adProvidersMap = new();
        private static Dictionary<AdProviderType, bool> _adProvidersStatusMap = new();

        private static bool _isDebugAd;
        private static bool _enableAd;


        private void OnEnable()
        {
            AdProvider.onInitComplete.AddListener(OnAdProviderInitialized);
        }

        private void OnDisable()
        {
            AdProvider.onInitComplete.RemoveListener(OnAdProviderInitialized);
        }

        protected override void Awake()
        {
            base.Awake();
            CreateAdProvider();
        }

        private void Start()
        {
            SetDebugAd(isDebugAd);
            SetEnableAd(Gm.EnableAds);
            
            InitAdProvider();
        }


        public static AdProvider GetAdProvider(AdProviderType providerType)
        {
            if (_adProvidersMap.TryGetValue(providerType, out var adProvider))
            {
                return adProvider;
            }

            BhDebug.LogWarning($"Ad provider {providerType} not found in the map.");
            return null;
        }
        
        public static bool TryGetAdProvider(AdProviderType providerType, out AdProvider adProvider)
        {
            if (_adProvidersMap.TryGetValue(providerType, out var adProvider1))
            {
                adProvider = adProvider1;
                return true;
            }

            BhDebug.LogWarning($"Ad provider {providerType} not found in the map.");
            adProvider = null;
            return false;
        }
        
        


        #region Init


        #region Init Ad Providers


        void CreateAdProvider()
        {
            _adProvidersMap.Add(AdProviderType.Applovin, new ApplovinAdProvider());
            _adProvidersMap.Add(AdProviderType.Admob, new AdmobAdProvider());
        }
        
        void InitAdProvider()
        {
            _adConfig = Cfg.adConfig;
            
            foreach (var adProvider in _adProvidersMap.Values)
            {
                adProvider.Init();
            }
        }
        
        
        public static void SetDebugAd(bool isDebug)
        {
            _isDebugAd = isDebug;
        }
        
        public static void SetEnableAd(bool isEnable)
        {
            _enableAd = isEnable;
        }

        
        public static bool IsAdProviderInitialized(AdProviderType providerType)
        {
            if (_adProvidersStatusMap.TryGetValue(providerType, out var isInitialized))
            {
                return isInitialized;
            }

            BhDebug.LogWarning($"Ad provider {providerType} not found in the status map.");
            return false;
        }
        
        void OnAdProviderInitialized(AdProviderType providerType)
        {
            Debug.Log($"Ad provider {providerType} initialized!!!");
            
            _adProvidersStatusMap.Add(providerType, true);
            InitAdUnits(providerType);
        }
        
        #endregion



        #region Init Ad Unit


        void InitAdUnits(AdProviderType adProviderType)
        {
            if (TryGetAdProvider(adProviderType, out var adProvider))
            {
                adProvider.InitAdUnits(_adConfig.GetAdProviderConfig(adProviderType).GetAdUnitDataConfigs());
            }
        }
        

        #endregion
        
        
        #endregion





        #region Ad Methods
        
        
        public static void CreateAd(AdProviderType adProviderType, string adUnitName)
        {
            // Create ad unit based on the provider type
            if (TryGetAdProvider(adProviderType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    adUnitItem.CreateAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {adProviderType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {adProviderType} not found.");
            }
        }
        
        public static void LoadAd(AdProviderType adProviderType, string adUnitName)
        {
            // Load ad based on the provider type
            if (TryGetAdProvider(adProviderType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    adUnitItem.LoadAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {adProviderType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {adProviderType} not found.");
            }
        }
        
        public static void DestroyAd(AdProviderType adProviderType, string adUnitName)
        {
            // Destroy ad unit based on the provider type
            if (TryGetAdProvider(adProviderType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    adUnitItem.DestroyAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {adProviderType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {adProviderType} not found.");
            }
        }

        public static bool CanShowAd(AdProviderType adProviderType, string adUnitName)
        {
            if (_isDebugAd)
                return false;
            
            // Check if the ad provider is initialized
            if (TryGetAdProvider(adProviderType, out var adProvider))
            {
                // Check if the ad unit is available
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    if (!_enableAd && adUnitItem.Config.GetAdType() != AdType.Rewarded)
                    {
                        return false;
                    }
                    return adUnitItem.CanShowAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {adProviderType}.");
                    return false;
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {adProviderType} not found.");
                return false;
            }
        }

        public static void ShowAd(AdProviderType adProviderType, string adUnitName, Action onShowComplete = null)
        {
            if (_isDebugAd)
                return;
            
            // Show ad based on the provider type
            if (TryGetAdProvider(adProviderType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    if (!_enableAd && adUnitItem.Config.GetAdType() != AdType.Rewarded)
                    {
                        return;
                    }
                    
                    adUnitItem.ShowAdUnitItem(onShowComplete);
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {adProviderType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {adProviderType} not found.");
            }
        }
        
        public static void HideAd(AdProviderType adProviderType, string adUnitName)
        {
            // Hide ad based on the provider type
            if (TryGetAdProvider(adProviderType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    adUnitItem.HideAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {adProviderType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {adProviderType} not found.");
            }
        }

        public static void ShowAppOpen(AdProviderType providerType, string adUnitName)
        {
            if (_isDebugAd)
                return;
            
            // Show app open ad
            if (TryGetAdProvider(providerType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    if (!_enableAd && adUnitItem.Config.GetAdType() != AdType.AppOpen)
                    {
                        return;
                    }
                    
                    adUnitItem.ShowAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {providerType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {providerType} not found.");
            }
        }
        
        public static void ShowInterstitial(AdProviderType providerType, string adUnitName)
        {
            if (_isDebugAd)
                return;
            
            // Show interstitial ad
            if (TryGetAdProvider(providerType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    if (!_enableAd && adUnitItem.Config.GetAdType() != AdType.Interstitial)
                    {
                        return;
                    }
                    
                    adUnitItem.ShowAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {providerType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {providerType} not found.");
            }
        }
        
        public static void ShowRewarded(AdProviderType providerType, string adUnitName, Action onShowComplete = null)
        {
            if (_isDebugAd)
                return;
            
            // Show rewarded ad
            if (TryGetAdProvider(providerType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    adUnitItem.ShowAdUnitItem(onShowComplete);
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {providerType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {providerType} not found.");
            }
        }
        
        public static void ShowBanner(AdProviderType providerType, string adUnitName)
        {
            if (_isDebugAd)
                return;
            // Show banner ad
            if (TryGetAdProvider(providerType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    if (!_enableAd && adUnitItem.Config.GetAdType() != AdType.Banner)
                    {
                        return;
                    }
                    adUnitItem.ShowAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {providerType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {providerType} not found.");
            }
        }
        
        public static void HideBanner(AdProviderType providerType, string adUnitName)
        {
            // Hide banner ad
            if (TryGetAdProvider(providerType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    adUnitItem.HideAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {providerType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {providerType} not found.");
            }
        }
        
        public static void ShowMRec(AdProviderType providerType, string adUnitName)
        {
            if (_isDebugAd)
                return;
            // Show MRec ad
            if (TryGetAdProvider(providerType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    if (!_enableAd && adUnitItem.Config.GetAdType() != AdType.MediumRectangle)
                    {
                        return;
                    }
                    
                    adUnitItem.ShowAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {providerType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {providerType} not found.");
            }
        }
        
        public static void HideMRec(AdProviderType providerType, string adUnitName)
        {
            // Hide MRec ad
            if (TryGetAdProvider(providerType, out var adProvider))
            {
                if (adProvider.TryGetAdUnitItem(adUnitName, out var adUnitItem))
                {
                    adUnitItem.HideAdUnitItem();
                }
                else
                {
                    BhDebug.LogWarning($"Ad unit {adUnitName} not found in provider {providerType}.");
                }
            }
            else
            {
                BhDebug.LogWarning($"Ad provider {providerType} not found.");
            }
        }

        #endregion
    }
}