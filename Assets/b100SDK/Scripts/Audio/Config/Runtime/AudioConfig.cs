using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Tool;
using UnityEngine;

namespace b100SDK.Scripts.Audio.Config.Runtime
{
    [CreateAssetMenu(fileName = "Audio Config", menuName = "b100 SDK/Configs/Core/Audio Config")]
    public class AudioConfig : ScriptableObject
    {
        [Header("Music")]
        [SerializeField]
        private SerializedDictionary<MusicType, string> musicEnumPathMap;
        
        [SerializeField]
        private SerializedDictionary<string, string> musicStringPathMap;
        
        [Header("Sound")]
        [SerializeField]
        private SerializedDictionary<SoundType, string> soundEnumPathMap;
        
        [SerializeField]
        private SerializedDictionary<string, string> soundStringPathMap;
        

        public string GetMusicPath(MusicType musicType)
        {
            if (musicEnumPathMap.TryGetValue(musicType, out string path))
            {
                return path;
            }

            return null;
        }
        
        public string GetMusicPath(string musicName)
        {
            if (musicStringPathMap.TryGetValue(musicName, out string path))
            {
                return path;
            }

            return null;
        }

        public string GetSoundPath(SoundType soundType)
        {
            if (soundEnumPathMap.TryGetValue(soundType, out string path))
            {
                return path;
            }

            return null;
        }
        
        public string GetSoundPath(string soundName)
        {
            if (soundStringPathMap.TryGetValue(soundName, out string path))
            {
                return path;
            }

            return null;
        }

#if UNITY_EDITOR
        
        public void SetMusicPath(SerializedDictionary<MusicType, AudioClip> musicAudioClipMap)
        {
            foreach (var item in musicAudioClipMap)
            {
                if (musicEnumPathMap.ContainsKey(item.Key))
                {
                    musicEnumPathMap[item.Key] = UtilitiesTool.GetResourcePath(item.Value);
                }
                else
                {
                    musicEnumPathMap.TryAdd(item.Key, UtilitiesTool.GetResourcePath(item.Value));
                }
            }
            
            ConfigTool.SaveConfig(this);
        }

        public void SetMusicPath(SerializedDictionary<string, AudioClip> musicAudioClipMap)
        {
            foreach (var item in musicAudioClipMap)
            {
                if (musicStringPathMap.ContainsKey(item.Key))
                {
                    musicStringPathMap[item.Key] = UtilitiesTool.GetResourcePath(item.Value);
                }
                else
                {
                    musicStringPathMap.TryAdd(item.Key, UtilitiesTool.GetResourcePath(item.Value));
                }
            }
            
            ConfigTool.SaveConfig(this);
        }


        public void SetSoundPath(SerializedDictionary<SoundType, AudioClip> soundAudioClipMap)
        {
            foreach (var item in soundAudioClipMap)
            {
                if (soundEnumPathMap.ContainsKey(item.Key))
                {
                    soundEnumPathMap[item.Key] = UtilitiesTool.GetResourcePath(item.Value);
                }
                else
                {
                    soundEnumPathMap.TryAdd(item.Key, UtilitiesTool.GetResourcePath(item.Value));
                }
            }
            
            ConfigTool.SaveConfig(this);
        }
        
        
        public void SetSoundPath(SerializedDictionary<string, AudioClip> soundAudioClipMap)
        {
            foreach (var item in soundAudioClipMap)
            {
                if (soundStringPathMap.ContainsKey(item.Key))
                {
                    soundStringPathMap[item.Key] = UtilitiesTool.GetResourcePath(item.Value);
                }
                else
                {
                    soundStringPathMap.TryAdd(item.Key, UtilitiesTool.GetResourcePath(item.Value));
                }
            }
            
            ConfigTool.SaveConfig(this);
        }

#endif

    }
}