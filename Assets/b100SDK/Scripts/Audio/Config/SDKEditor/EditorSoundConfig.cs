#region

using b100SDK.Scripts.Audio.Config.Runtime;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Audio.Config.SDKEditor
{

    #endregion

    [CreateAssetMenu(fileName = "Editor Sound Config", menuName = "b100 SDK/Editor/Sound Config")]
    public class EditorSoundConfig : ScriptableObject
    {
        [Title("Music")]
        [SerializeField]
        private SerializedDictionary<MusicType, AudioClip> musicEnumMap;
        [PropertySpace(10f)]
        [SerializeField]
        private SerializedDictionary<string, AudioClip> musicStringMap;
        
        
        [PropertySpace(20f)]
        [PropertyOrder(20)]
        [Title("Sound")]
        [SerializeField]
        private SerializedDictionary<SoundType, AudioClip> soundEnumMap;
        
        [PropertySpace(10f)]
        [PropertyOrder(20)]
        [SerializeField]
        private SerializedDictionary<string, AudioClip> soundStringMap;

        [PropertySpace(20f)]
        [PropertyOrder(25)]
        [Title("Sound Resource Path")]
        [SerializeField, ReadOnly]
        [InlineEditor(InlineEditorObjectFieldModes.Boxed, Expanded = false)]
        private AudioConfig audioConfigResource;
        
        
        
#if UNITY_EDITOR

        [PropertyOrder(1)]
        [PropertySpace(10f, 10f)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void CreateMusic()
        {
            AudioCreator.OpenWindow(true);
        }       
        
        [PropertyOrder(1)]
        [PropertySpace(10f, 10f)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void CreateMusicString()
        {
            AudioCreatorString.OpenWindow(true);
        }

        [PropertyOrder(21)]
        [PropertySpace(10f, 10f)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void CreateSound()
        {
            AudioCreator.OpenWindow(false);
        }           
        
        [PropertyOrder(21)]
        [PropertySpace(10f, 10f)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void CreateSoundString()
        {
            AudioCreatorString.OpenWindow(false);
        }      
        
        
        
        
        [PropertyOrder(22)]
        [PropertySpace(20f, 10f)]
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        [GUIColor(0.5f, 0.1f, 0.1f)]
        void Close()
        {
            AudioCreator.CloseWindow();
            AudioCreatorString.CloseWindow();
        }

        
        





        public void AddSound(SerializedDictionary<SoundType, AudioClip> soundDic)
        {
            foreach (var soundKeyValuePair in soundDic)
            {
                if (soundEnumMap.ContainsKey(soundKeyValuePair.Key))
                {
                    soundEnumMap[soundKeyValuePair.Key] = soundKeyValuePair.Value;
                }
                else
                {
                    soundEnumMap.TryAdd(soundKeyValuePair.Key, soundKeyValuePair.Value);
                }
            }
            
            ConfigTool.SaveConfig(this);
            
            SaveConfig();
        }
        
        public void AddSound(SerializedDictionary<string, AudioClip> soundDic)
        {
            foreach (var soundKeyValuePair in soundDic)
            {
                if (soundStringMap.ContainsKey(soundKeyValuePair.Key))
                {
                    soundStringMap[soundKeyValuePair.Key] = soundKeyValuePair.Value;
                }
                else
                {
                    soundStringMap.TryAdd(soundKeyValuePair.Key, soundKeyValuePair.Value);
                }
            }
            
            ConfigTool.SaveConfig(this);
            
            SaveConfig();
        }


        public void AddMusic(SerializedDictionary<MusicType, AudioClip> musicDic)
        {
            foreach (var musicKeyValuePair in musicDic)
            {
                if (musicEnumMap.ContainsKey(musicKeyValuePair.Key))
                {
                    musicEnumMap[musicKeyValuePair.Key] = musicKeyValuePair.Value;
                }
                else
                {
                    musicEnumMap.TryAdd(musicKeyValuePair.Key, musicKeyValuePair.Value);
                }
            }
            
            ConfigTool.SaveConfig(this);
            
            SaveConfig();
        }
        
        public void AddMusic(SerializedDictionary<string, AudioClip> musicDic)
        {
            foreach (var musicKeyValuePair in musicDic)
            {
                if (musicStringMap.ContainsKey(musicKeyValuePair.Key))
                {
                    musicStringMap[musicKeyValuePair.Key] = musicKeyValuePair.Value;
                }
                else
                {
                    musicStringMap.TryAdd(musicKeyValuePair.Key, musicKeyValuePair.Value);
                }
            }
            
            ConfigTool.SaveConfig(this);
            
            SaveConfig();
        }
        
        
        
        
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        [PropertyOrder(30)]
        [PropertySpace(30f)]
        void SaveConfig()
        {
            var audioConfig = ConfigTool.GetAudioConfig();
            audioConfig.SetSoundPath(soundEnumMap);
            audioConfig.SetMusicPath(musicEnumMap);
            audioConfig.SetSoundPath(soundStringMap);
            audioConfig.SetMusicPath(musicStringMap);

            if (!audioConfigResource)
            {
                audioConfigResource = audioConfig;
                ConfigTool.SaveConfig(this);
            } 
        }
#endif
    }
}