using UnityEngine;

namespace b100SDK.Scripts.Base
{
    public class BhMonoBehavior : MonoBehaviour
    {
        private ConfigManager _cfg;
        private GameManager _gameManager;
        private EventGlobalManager _eventGlobalManager;

        protected GameManager Gm
        {
            get
            {
                if (!_gameManager)
                {
                    _gameManager = GameManager.Instance;
                }

                return _gameManager;
            }
        }

        protected EventGlobalManager Evm
        {
            get
            {
                if (!_eventGlobalManager)
                {
                    _eventGlobalManager = EventGlobalManager.Instance;
                }

                return _eventGlobalManager;
            }
        }

        protected ConfigManager Cfg
        {
            get
            {
                if (!_cfg)
                {
                    _cfg = ConfigManager.Instance;
                }

                return _cfg;
            }
        }
    }
}