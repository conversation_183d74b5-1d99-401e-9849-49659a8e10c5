using System.Linq;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector.Editor;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Editor
{
    public class BhSdkPanel : OdinMenuEditorWindow
    {
        private const string _ICON_MENU_PATH = "Assets/b100SDK/SDK Resources/IconSDK/";
        
        
        private Sprite _iconGameSetting; 
        private Sprite _iconGameConfig; 
        private Sprite _iconSoundConfig; 
        private Sprite _iconUiConfig; 
        private Sprite _iconIAPConfig; 
        private Sprite _iconAdConfig; 
        private Sprite _iconRemoteConfig;
        private Sprite _iconBuilder;
        private Sprite _iconPushNotification;

        private Sprite _iconPackage;
        
        
        
        
        [MenuItem("b100/Open Bh SDK Panel", priority = 0)]
        public static void OpenWindow()
        {
            GetWindow<BhSdkPanel>(title: "b100 SDK Panel").Show();
        }

        protected override OdinMenuTree BuildMenuTree()
        {
            var tree = new OdinMenuTree();

            tree.Add("Game Setting", SettingTool.GetGameSetting(), _iconGameSetting);
            tree.Add("Game Config", ConfigTool.GetGameConfig(), _iconGameConfig);
            tree.Add("Sound Config", ConfigTool.GetSoundConfig(), _iconSoundConfig);
            tree.Add("UI Config", ConfigTool.GetEditorUiConfig(), _iconUiConfig);
            tree.Add("IAP Config", ConfigTool.GetIAPConfig(), _iconIAPConfig);
            tree.Add("Ad Config", ConfigTool.GetAdConfig(), _iconAdConfig);
            tree.Add("Remote Config", ConfigTool.GetRemoteConfigGroup(), _iconRemoteConfig);
            tree.Add("Push Notification", ConfigTool.GetNotificationConfig(), _iconPushNotification);
            tree.Add("Builder", BuilderTool.GetGameBuilder(), _iconBuilder);
            tree.Add("Package", ConfigTool.GetPackageConfig(), _iconPackage);

            return tree;
        }

        protected override void Initialize()
        {
            base.Initialize();

            var sprites = UtilitiesTool.GetSprite(_ICON_MENU_PATH);

            _iconGameSetting = sprites.FirstOrDefault(x => x.name == "IconGameSetting");
            _iconGameConfig = sprites.FirstOrDefault(x => x.name == "IconGameConfig");
            _iconSoundConfig = sprites.FirstOrDefault(x => x.name == "IconSoundConfig");
            _iconUiConfig = sprites.FirstOrDefault(x => x.name == "IconUiConfig");
            _iconIAPConfig = sprites.FirstOrDefault(x => x.name == "IconIAPConfig");
            _iconAdConfig = sprites.FirstOrDefault(x => x.name == "IconIAAConfig");
            _iconRemoteConfig = sprites.FirstOrDefault(x => x.name == "IconRemoteConfig");
            _iconBuilder = sprites.FirstOrDefault(x => x.name == "IconBuilder");
            _iconPushNotification = sprites.FirstOrDefault(x => x.name == "IconPushNotification");
            _iconPackage = sprites.FirstOrDefault(x => x.name == "IconPackage");

            ConfigTool.GetPackageConfig().LoadPackageDataOnInit();
        }
    }
}