using System.Collections.Generic;
using b100SDK.Scripts.UI.Components.Leaderboard;

namespace b100SDK.Scripts.ExtraFeature.Leaderboard
{
    public interface ILeaderboardUserStrategy
    {
        public int GetTotalPlayerCount();
        public int GetLeaderboardCount();
        
        public LeaderboardData GetPlayerLeaderboardData();
        public List<LeaderboardData> GetUserLeaderboardData();
        
        public int CompareLeaderboardData(LeaderboardData x, LeaderboardData y);
        
        public void SortLeaderboardData(List<LeaderboardData> data);
        
        public bool NeedResetData();
    }
}