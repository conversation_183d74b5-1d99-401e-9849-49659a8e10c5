using System.Threading;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;

#if ENABLE_GOOGLE_SERVICES
using Google.Apis.Auth.OAuth2;
#endif

namespace b100SDK.Scripts.Google
{
    public static class GoogleAuthentication
    {
#if ENABLE_GOOGLE_SERVICES
        
        public static UserCredential GetAuthentication(string googleClientId, string googleClientSecret, string[] scopes)
        {
            ClientSecrets secrets = new ClientSecrets()
            {
                ClientId = googleClientId,
                ClientSecret = googleClientSecret,
            };

            return GoogleWebAuthorizationBroker.AuthorizeAsync(secrets, scopes, "user", CancellationToken.None).Result;
        }
        
        public static async UniTask<UserCredential> GetAuthenticationAsync(string googleClientId, string googleClientSecret, string[] scopes)
        {
            ClientSecrets secrets = new ClientSecrets()
            {
                ClientId = googleClientId,
                ClientSecret = googleClientSecret,
            };

            return await GoogleWebAuthorizationBroker.AuthorizeAsync(secrets, scopes, "user", CancellationToken.None);
        }
#endif

    }
}