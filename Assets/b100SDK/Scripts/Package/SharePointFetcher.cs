using System;
using System.Collections;
using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using UnityEngine;
using UnityEngine.Networking;

namespace b100SDK.Scripts.Package
{
    public class SharePointFetcher : BhMonoBehavior
    {
        [Header("Azure App Credentials")]
        public string tenantId = "b33b66ad-09fd-489f-a71c-c62bf9383147";

        public string clientId = "615a4d23-5f15-41a3-a4d7-605e3041e662";
        
        [TextArea]
        public string clientSecret = "your-client-secret";

        [Header("SharePoint Settings")]
        public string sharepointDomain = "https://1pkhny.sharepoint.com";

        public string siteName = "UnityPackage"; // Tên site bạn tạo trên SharePoint
        public string folderPath = "UnityPackage";
        public string fileNameToDownload = "GoogleMobileAds-native.unitypackage";

        void Start()
        {
            StartCoroutine(GetAccessToken(token => { StartCoroutine(DownloadFileFromSharePoint(token)); }));
        }

        IEnumerator GetAccessToken(Action<string> onTokenReceived)
        {
            string url = $"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token";
            WWWForm form = new WWWForm();
            form.AddField("grant_type", "client_credentials");
            form.AddField("client_id", clientId);
            form.AddField("client_secret", clientSecret);
            form.AddField("scope", "https://graph.microsoft.com/.default");

            using UnityWebRequest req = UnityWebRequest.Post(url, form);
            yield return req.SendWebRequest();

            if (req.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError("Token Error: " + req.downloadHandler.text);
            }
            else
            {
                var json = JsonUtility.FromJson<AccessTokenResponse>(req.downloadHandler.text);
                onTokenReceived?.Invoke(json.access_token);
            }
        }

        IEnumerator DownloadFileFromSharePoint(string token)
        {
            string siteId = null;
            string driveId = null;

            // Get siteId
            string siteSearchUrl = $"https://graph.microsoft.com/v1.0/sites?search={siteName}";
            UnityWebRequest siteReq = UnityWebRequest.Get(siteSearchUrl);
            siteReq.SetRequestHeader("Authorization", "Bearer " + token);
            yield return siteReq.SendWebRequest();

            if (siteReq.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError("Site ID Error: " + siteReq.downloadHandler.text);
                yield break;
            }

            var siteList = JsonUtility.FromJson<SiteList>(siteReq.downloadHandler.text);
            foreach (var s in siteList.value)
            {
                if (s.webUrl.Contains(siteName))
                {
                    siteId = s.id;
                    break;
                }
            }

            if (string.IsNullOrEmpty(siteId))
            {
                Debug.LogError("Site not found");
                yield break;
            }

            // Get driveId
            string driveUrl = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drives";
            UnityWebRequest driveReq = UnityWebRequest.Get(driveUrl);
            driveReq.SetRequestHeader("Authorization", "Bearer " + token);
            yield return driveReq.SendWebRequest();

            if (driveReq.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError("Drive Error: " + driveReq.downloadHandler.text);
                yield break;
            }

            var drives = JsonUtility.FromJson<DriveList>(driveReq.downloadHandler.text);
            foreach (var d in drives.value)
            {
                if (d.name == "Documents")
                {
                    driveId = d.id;
                    break;
                }
            }

            if (string.IsNullOrEmpty(driveId))
            {
                Debug.LogError("Drive not found");
                yield break;
            }
            
            BhDebug.Log("Site id: " + siteId);
            BhDebug.Log("Drive id: " + driveId );

            // List files in folder
            string listUrl =
                $"https://graph.microsoft.com/v1.0/sites/{siteId}/drives/{driveId}/root:/{folderPath}:/children";
            UnityWebRequest listReq = UnityWebRequest.Get(listUrl);
            listReq.SetRequestHeader("Authorization", "Bearer " + token);
            yield return listReq.SendWebRequest();

            if (listReq.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError("List Error: " + listReq.downloadHandler.text);
                yield break;
            }

            var list = JsonUtility.FromJson<GraphDriveItemList>(FixJsonArray(listReq.downloadHandler.text));
            foreach (var item in list.value)
            {
                if (item.name == fileNameToDownload)
                {
                    Debug.Log("Found file: " + item.name);
                    StartCoroutine(DownloadFile(item.downloadUrl, item.name));
                    yield break;
                }
            }

            Debug.LogWarning("File not found: " + fileNameToDownload);
        }

        IEnumerator DownloadFile(string url, string fileName)
        {
            UnityWebRequest req = UnityWebRequest.Get(url);
            yield return req.SendWebRequest();

            if (req.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError("Download Error: " + req.downloadHandler.text);
            }
            else
            {
                string path = Application.persistentDataPath + "/" + fileName;
                System.IO.File.WriteAllBytes(path, req.downloadHandler.data);
                Debug.Log("File downloaded to: " + path);
            }
        }

        // --- Helper classes ---
        [Serializable]
        public class AccessTokenResponse
        {
            public string access_token;
        }

        [Serializable]
        public class SiteList
        {
            public List<SiteInfo> value;
        }

        [Serializable]
        public class SiteInfo
        {
            public string id;
            public string webUrl;
        }

        [Serializable]
        public class DriveList
        {
            public List<DriveInfo> value;
        }

        [Serializable]
        public class DriveInfo
        {
            public string id;
            public string name;
        }

        [Serializable]
        public class GraphDriveItemList
        {
            public List<GraphDriveItem> value;
        }

        [Serializable]
        public class GraphDriveItem
        {
            public string name;
            public string downloadUrl;
        }

        // Fix Graph API JSON for Unity
        string FixJsonArray(string json) => json.Replace("@microsoft.graph.downloadUrl", "downloadUrl");
    }
}