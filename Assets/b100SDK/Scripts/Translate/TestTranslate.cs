using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.Utilities;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.Translate
{
    public class TestTranslate : BhMonoBehavior
    {
        [SerializeField, TextArea]
        private string textToTranslate;
        
        [SerializeField]
        private string fromCode = "en";
        
        [SerializeField]
        private string toCode = "vi";



        [<PERSON><PERSON>(ButtonSizes.Large, ButtonStyle.Box)]
        async void Translate()
        {
            var now = DateTime.Now;
            BhDebug.Log($"Start time: {now}");
            var result = await GoogleTranslate.Translate(textToTranslate, fromCode, toCode);
           
            Debug.Log(result?.translatedText);
            var endTime = DateTime.Now;
            BhDebug.Log($"End time: {endTime}, Elapsed time: {endTime - now}");
        }
    }
}