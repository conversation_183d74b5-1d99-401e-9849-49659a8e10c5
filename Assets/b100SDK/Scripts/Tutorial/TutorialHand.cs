using System.Collections;
using b100SDK.Scripts.Utilities;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

namespace b100SDK.Scripts.Tutorial
{
    public class TutorialHand : MonoBehaviour
    {
        [SerializeField]
        private Transform handTransform;

        [SerializeField]
        private GameObject dimBackgroundGroup;

        [SerializeField]
        private Transform holeContainerTransform;

        [SerializeField]
        private Transform holeIconTransform;

        [SerializeField]
        private CanvasGroup handCanvasGroup;

        [SerializeField]
        private Animator handTapAnimator;

        [SerializeField]
        private Image handIconImage;

        [SerializeField]
        private Sprite dragHandIcon;

        [Space(20.0f)]
        [Header("Arrow")]
        [SerializeField]
        private TutorialArrow tutorialArrow;

        [SerializeField]
        private float arrowShowScreenThreshold = 100;

        [SerializeField]
        private float arrowShowWorldThreshold = 2.0f;


        [Space(20.0f)]
        [Header("Drag & drop")]
        [SerializeField]
        private float appearDuration = 0.5f;

        [SerializeField]
        private float screenDragSpeed = 700.0f;

        [SerializeField]
        private float worldDragSpeed = 5.0f;

        [SerializeField]
        private float postDragWait = 0.3f;

        [SerializeField]
        private float appearScale = 1.3f;

        [SerializeField]
        private float appearOpacity = 0.25f;

        [SerializeField]
        private float disappearOpacity = 0.0f;

        [SerializeField]
        private float loopDelay = 0.5f;


        [Space(20.0f)]
        [Header("Tap")]
        [SerializeField]
        private float holeIconShrinkDuration = 1.0f;

        [Header("Hole")]
        [SerializeField]
        private Image holeImage;

        [SerializeField]
        private RectTransform holeContainerRectTransform;

        [SerializeField]
        private SerializedDictionary<HoleType, Sprite> holeMap;

        [SerializeField]
        private float holeRate = 1.2f;

        [SerializeField]
        private Vector2 defaultHoleSize = Vector2.one * 150f;


        private Camera _mainCamera;

        void OnEnable()
        {
            InitializeCamera();
        }

        void OnDisable()
        {
            StopAll();
        }

        public void Hide()
        {
            this.gameObject.SetActive(false);
        }

        private void StopAll()
        {
            this.DOKill();
            handTapAnimator.enabled = false;
        }

        public void DoHandDragAndDrop(Vector3 startPosition, Vector3 endPosition, bool translateWorldToScreen,
            bool showArrow = false)
        {
            showArrow = showArrow && CheckIfArrowInShowThreshold(startPosition, endPosition, translateWorldToScreen);

            this.DOKill();
            gameObject.SetActive(true);
            dimBackgroundGroup.SetActive(false);
            tutorialArrow.ToggleVisibility(showArrow);

            handTapAnimator.enabled = false;
            handIconImage.sprite = dragHandIcon;

            StartCoroutine(HandDragAndDropSequence(startPosition, endPosition, translateWorldToScreen, showArrow));
        }

        public void DoHandDragAndDrop(Transform startPoint, Transform endPoint, bool showArrow = false)
        {
            this.DOKill();
            gameObject.SetActive(true);
            dimBackgroundGroup.SetActive(false);
            tutorialArrow.ToggleVisibility(showArrow);

            handTapAnimator.enabled = false;
            handIconImage.sprite = dragHandIcon;

            StartCoroutine(HandDragAndDropSequence(startPoint, endPoint, showArrow));
        }

        public void DoHandTap(Vector3 screenPosition, bool showDimBackground = false,
            bool animateDimBackgroundShrink = false, HoleType holeType = HoleType.Circle, Vector2? holeSize = null)
        {
            if (holeSize == null)
            {
                holeContainerRectTransform.sizeDelta = defaultHoleSize;
                holeImage.rectTransform.sizeDelta = defaultHoleSize;
            }
            else
            {
                holeImage.rectTransform.sizeDelta = holeSize.Value * holeRate;
                holeContainerRectTransform.sizeDelta = holeSize.Value;
            }

            holeMap.TryGetValue(holeType, out var holeSprite);

            if (holeSprite)
            {
                holeImage.sprite = holeSprite;
            }
            else
            {
                holeImage.sprite = holeMap[HoleType.Circle];
            }




            this.DOKill();
            gameObject.SetActive(true);
            dimBackgroundGroup.SetActive(false);
            tutorialArrow.ToggleVisibility(false);

            handTransform.position = screenPosition;
            handTapAnimator.enabled = false;
            handCanvasGroup.alpha = 0.0f;

            if (showDimBackground)
            {
                holeContainerTransform.position = screenPosition;
                dimBackgroundGroup.SetActive(true);

                holeIconTransform.localScale = Vector3.one;

                if (animateDimBackgroundShrink)
                {
                    holeIconTransform
                        .DOScale(Vector3.one, holeIconShrinkDuration)
                        .From(Vector3.one * 20.0f)
                        .SetEase(Ease.OutSine)
                        .OnComplete(() => { ShowHand(); })
                        .SetTarget(this);
                }
            }

            if (!(showDimBackground && animateDimBackgroundShrink))
            {
                ShowHand();
            }

            void ShowHand()
            {
                handTapAnimator.enabled = true;
                handCanvasGroup.alpha = 1.0f;
            }
        }

        private IEnumerator HandDragAndDropSequence(Vector3 startPosition, Vector3 endPosition,
            bool translateWorldToScreen, bool showArrow)
        {
            if (translateWorldToScreen)
            {
                startPosition = _mainCamera.WorldToScreenPoint(startPosition);
                startPosition.z = 0.0f;

                endPosition = _mainCamera.WorldToScreenPoint(endPosition);
                endPosition.z = 0.0f;
            }

            handCanvasGroup.alpha = 0.0f;
            float dragSpeed = translateWorldToScreen ? screenDragSpeed : worldDragSpeed;
            float dragDuration = Vector3.Distance(startPosition, endPosition) / dragSpeed;

            while (true)
            {
                handTransform.position = startPosition;
                if (showArrow)
                {
                    tutorialArrow.SetPosition(startPosition, endPosition);
                    tutorialArrow.SetMaskValue(0.0f);
                }

                yield return DOTween.Sequence()
                    .Insert(0.0f, handTransform
                        .DOScale(1.0f, appearDuration)
                        .From(appearScale))
                    .Insert(0.0f, handCanvasGroup
                        .DOFade(1.0f, appearDuration)
                        .From(appearOpacity));

                yield return DOVirtual.Float(0.0f, 1.0f, dragDuration, UpdateHandPosition)
                    .SetEase(Ease.OutSine);

                yield return DOTween.Sequence()
                    .Insert(postDragWait, handTransform
                        .DOScale(appearScale, appearDuration)
                        .From(1.0f))
                    .Insert(postDragWait, handCanvasGroup
                        .DOFade(disappearOpacity, appearDuration))
                    .AppendInterval(loopDelay);

                yield return YieldPool.Wait(dragDuration + loopDelay);
            }

            void UpdateHandPosition(float t)
            {
                handTransform.position = Vector3.LerpUnclamped(startPosition, endPosition, t);
                if (showArrow)
                {
                    tutorialArrow.SetMaskValue(t);
                }
            }
        }

        private IEnumerator HandDragAndDropSequence(Transform startPoint, Transform endPoint, bool showArrow)
        {
            handCanvasGroup.alpha = 0.0f;
            yield return DOTween.Sequence().AppendInterval(0.1f);

            while (true)
            {
                // Calculation
                Vector3 screenStartPosition = _mainCamera.WorldToScreenPoint(startPoint.position);
                screenStartPosition.z = 0.0f;

                Vector3 screenEndPosition = _mainCamera.WorldToScreenPoint(endPoint.position);
                screenEndPosition.z = 0.0f;

                float dragDuration = Vector3.Distance(screenStartPosition, screenEndPosition) / screenDragSpeed;

                bool shouldShowArrow =
                    showArrow && CheckIfArrowInShowThreshold(screenStartPosition, screenEndPosition, true);

                // Init
                handTransform.position = screenStartPosition;
                if (shouldShowArrow)
                {
                    tutorialArrow.SetPosition(screenStartPosition, screenEndPosition);
                    tutorialArrow.SetMaskValue(0.0f);
                }

                // Run
                yield return DOTween.Sequence()
                    .Insert(0.0f, handTransform
                        .DOScale(1.0f, appearDuration)
                        .From(appearScale))
                    .Insert(0.0f, handCanvasGroup
                        .DOFade(1.0f, appearDuration)
                        .From(appearOpacity));


                yield return DOVirtual.Float(0.0f, 1.0f, dragDuration, UpdateHandPosition)
                    .SetEase(Ease.OutSine);

                yield return DOTween.Sequence()
                    .Insert(postDragWait, handTransform
                        .DOScale(appearScale, appearDuration)
                        .From(1.0f))
                    .Insert(postDragWait, handCanvasGroup
                        .DOFade(disappearOpacity, appearDuration))
                    .AppendInterval(loopDelay);

                yield return YieldPool.Wait(dragDuration + loopDelay);


                void UpdateHandPosition(float t)
                {
                    handTransform.position = Vector3.LerpUnclamped(screenStartPosition, screenEndPosition, t);
                    if (shouldShowArrow)
                    {
                        tutorialArrow.SetMaskValue(t);
                    }
                }
            }
        }

        private bool CheckIfArrowInShowThreshold(Vector3 startPosition, Vector3 endPosition, bool inScreenSpace)
        {
            float dragDistance = (endPosition - startPosition).magnitude;

            if (inScreenSpace)
            {
                return dragDistance >= arrowShowScreenThreshold;
            }
            else
            {
                return dragDistance >= arrowShowWorldThreshold;
            }
        }

        private void InitializeCamera()
        {
            if (_mainCamera == null)
            {
                _mainCamera = Camera.main;
            }
        }


#if UNITY_EDITOR
        [Button]
        private void AnimateHoleShrink()
        {
            holeIconTransform.DOKill();
            holeIconTransform
                .DOScale(Vector3.one, holeIconShrinkDuration)
                .From(Vector3.one * 20.0f);
        }

#endif
    }
}

public enum HoleType
{
    Circle,
    Capsule,
    Square,
}