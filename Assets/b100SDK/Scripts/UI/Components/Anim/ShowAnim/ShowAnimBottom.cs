using System;
using b100SDK.Scripts.Base;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Anim.ShowAnim
{
    [RequireComponent(typeof(RectTransform))]
    public class ShowAnimBottom : MonoBehaviour, IShowAnimatable
    {
        [Space]
        [Header("Components")]
        [SerializeField]
        private RectTransform rectTransform;
        
        [SerializeField]
        private RectTransform container;
        
        [Space]
        [Header("Configs")]
        [SerializeField]
        private float showAnimTime = .35f;
        
        [SerializeField]
        private float delayShowAnim = 0f;

        [SerializeField]
        private EasingType easingType = EasingType.InSine;
        
        [ShowIf(nameof(UseShowAnimCurve))]
        [SerializeField]
        private AnimationCurve showAnimCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        
        [Space]
        [SerializeField]
        private bool unscaleTime = true;

        private Vector3 _initPosition;

        private bool UseShowAnimCurve
        {
            get
            {
                return easingType == EasingType.Custom;
            }
        }


        public void SetUp()
        {
            _initPosition = rectTransform.localPosition;
        }

        public float GetTimeExecute()
        {
            return showAnimTime + delayShowAnim;
        }

        public void Execute(bool instant = false, Action onComplete = null, params object[] extraParams)
        {
            if (!container)
                return;

            if (!rectTransform) 
                return;
            
            Rect rootRect = container.rect;
            Rect popupRect = rectTransform.rect;

            rectTransform.DOKill();
            rectTransform.localPosition = _initPosition;  // Reset to initial position before animating.
            
            if (instant)
            {
                onComplete?.Invoke();
                return;
            }

            //float offset = rootRect.height / 2 + popupRect.height / 2;
            float offset = rootRect.height / 2 + popupRect.height * (1 - rectTransform.pivot.y);


            rectTransform.localPosition += Vector3.down * offset;

            var tween = rectTransform.DOLocalMoveY(offset, showAnimTime)
                .SetDelay(delayShowAnim)
                .SetUpdate(unscaleTime)
                .SetRelative(true)
                .OnComplete(() => onComplete?.Invoke())
                .SetTarget(rectTransform);

            if (UseShowAnimCurve)
                tween.SetEase(showAnimCurve);
            else
                tween.SetEase((Ease) easingType);
        }


#if UNITY_EDITOR
        private void Reset()
        {
            GetRectTransform();


            container = rectTransform.parent.GetComponent<RectTransform>();
            
            void GetRectTransform()
            {
                var rectTransformPopup = GetComponentsInChildren<RectTransform>();

                foreach (var rect in rectTransformPopup)
                {
                    if (rect.name.Contains("Popup") && rect.gameObject != gameObject)
                    {
                        rectTransform = rect;
                        return;
                    }
                }

                rectTransform = GetComponent<RectTransform>();
            }

            var animTop = GetComponent<ShowAnimTop>();

            if (animTop)
            {
                DestroyImmediate(animTop);
            }
        }
#endif
    }
}