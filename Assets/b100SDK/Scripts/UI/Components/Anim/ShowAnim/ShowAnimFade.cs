using System;
using b100SDK.Scripts.Base;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Anim.ShowAnim
{
    [RequireComponent(typeof(CanvasGroup))]
    public class ShowAnimFade : MonoBehaviour, IShowAnimatable
    {
        [Space]
        [Header("Components")]
        [SerializeField]
        private CanvasGroup canvasGroup;
        
        [Space]
        [Header("Configs")]
        [SerializeField]
        private float showAnimTime = .35f;
        
        [SerializeField]
        private float delayShowAnim = 0f;
        
        [SerializeField]
        private EasingType easingType = EasingType.InSine;
        
        [SerializeField, ShowIf(nameof(UseShowAnimCurve))]
        private AnimationCurve showAnimCurve;
        
        [Space]
        [SerializeField] 
        private bool unscaleTime = true;
        
        private bool UseShowAnimCurve => easingType == EasingType.Custom;

        public void SetUp()
        {
            
        }
        
        public float GetTimeExecute()
        {
            return showAnimTime + delayShowAnim;
        }

        public void Execute(bool instant = false, Action onComplete = null, params object[] extraParams)
        {
            if (!canvasGroup)
                return;
            
            canvasGroup.DOKill();
            canvasGroup.alpha = 0;
            
            if (instant)
            {
                canvasGroup.alpha = 1f;
                onComplete?.Invoke();
                return;
            }

            var tween = canvasGroup.DOFade(1, showAnimTime)
                .SetUpdate(unscaleTime)
                .OnComplete(() => onComplete?.Invoke())
                .SetTarget(canvasGroup)
                .SetDelay(delayShowAnim);

            if (UseShowAnimCurve)
                tween.SetEase(showAnimCurve);
            else
                tween.SetEase((Ease)easingType);
            
        }


#if UNITY_EDITOR
        private void Reset()
        {
            canvasGroup = GetComponent<CanvasGroup>();
        }
#endif
    }
}