using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Components.Button;
using b100SDK.Scripts.UI.Panel;
using b100SDK.Scripts.Utilities.Extensions;
using TMPro;
using UnityEngine;

namespace b100SDK.Scripts.UI.Components.Online_Reward
{
    public class OnlineRewardButton : BhMonoBehavior
    {
        [SerializeField]
        private BhButton button;
        
        [SerializeField] 
        private TMP_Text timer;
        
        [SerializeField] 
        private GameObject notif;

        private void OnEnable()
        {
            button.onClick.AddListener(OpenPopup);
            
            Evm.OnEverySecondTick.AddListener(UpdateTimer);
            UpdateTimer();
        }

        private void OnDisable()
        {
            button.onClick.RemoveListener(OpenPopup);

            if (Evm)
                Evm.OnEverySecondTick.AddListener(UpdateTimer);
        }
    
        void UpdateTimer()
        {
            int timeRemain = PopupOnlineReward.GetRemainTime();

            if (timeRemain < 0)
            {
                notif.SetActive(true);
                timer.text = "Claim";
            }
            else
            {
                notif.SetActive(false);
                timer.text = timeRemain.ToTimeFormatCompact();
            }
        }

        void OpenPopup()
        {
            PopupOnlineReward.Show();
        }
    }
}
