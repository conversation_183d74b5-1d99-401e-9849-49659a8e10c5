using System;
using System.Collections;
using System.Collections.Generic;
using b100SDK.Scripts.Ads;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.DesignPatterns;
using b100SDK.Scripts.Utilities;
using Sigtrap.Relays;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace b100SDK.Scripts.UI
{
    public delegate bool CanShowAoa();
    
    public class LoadingManager : BhSingletonPersistent<LoadingManager>
    {
        private readonly Dictionary<SceneIndex, Action> _sceneLoadedCallback = new();
        public readonly Relay onStartShowAoa = new();
        public event CanShowAoa OnCanShowAoa;

        void SetSceneAction(SceneIndex scene, Action action)
        {
            if (_sceneLoadedCallback.ContainsKey(scene))
            {
                _sceneLoadedCallback[scene] = action;
            }
            else
            {
                _sceneLoadedCallback.TryAdd(scene, action);
            }
        }

        public void LoadScene(SceneIndex type, Action onComplete = null, float loadingDuration = 3f, bool showAoa = true)
        {
            if (onComplete != null)
            {
                SetSceneAction(type, onComplete);
            }

            Evm.OnStartLoadScene.Dispatch();

            if (showAoa)
            {
                StartCoroutine(LoadSceneAsyncShowAoa(type, loadingDuration));
            }
            else
            {
                StartCoroutine(LoadSceneAsync(type, loadingDuration));
            }
        }

        
        private IEnumerator LoadSceneAsync(SceneIndex index, float loadingDuration = 3f)
        {
            float loadingSpeed = .9f / loadingDuration;

            yield return YieldPool.Wait(0.1f);
            
            var asyncLoad = SceneManager.LoadSceneAsync((int)index);
            
            if (asyncLoad != null)
            {
                asyncLoad.allowSceneActivation = false;

                Application.backgroundLoadingPriority = ThreadPriority.Normal;

                float loadingTime = 0f;

                while (!asyncLoad.isDone)
                {
                    Evm.OnChangeProgressLoading.Dispatch(asyncLoad.progress, loadingSpeed);

                    loadingTime += Time.deltaTime;

                    if (asyncLoad.progress >= 0.9f && loadingTime > loadingDuration)
                    {
                        asyncLoad.allowSceneActivation = true;

                        if (asyncLoad.isDone)
                            break;
                    }

                    yield return null;
                }
            }

            GC.Collect(2, GCCollectionMode.Forced);
            Resources.UnloadUnusedAssets();

            float loadingTimeLeft = .1f / loadingSpeed + .1f;

            Evm.OnChangeProgressLoading.Dispatch(1f, loadingSpeed);
            yield return new WaitForSeconds(loadingTimeLeft);

            Evm.OnFinishLoadScene.Dispatch();

            if (_sceneLoadedCallback.ContainsKey(index))
            {
                _sceneLoadedCallback[index]?.Invoke();
            }

        }



        
        private IEnumerator LoadSceneAsyncShowAoa(SceneIndex index, float loadingDuration = 3f)
        {
            var isShowAoa = false;
            
            float loadingSpeed =.9f / loadingDuration;
            
            yield return YieldPool.Wait(0.1f);
            
            var asyncLoad = SceneManager.LoadSceneAsync((int) index);
            if (asyncLoad != null)
            {
                asyncLoad.allowSceneActivation = false;

                Application.backgroundLoadingPriority = ThreadPriority.Normal;

#if ENABLE_ADS
                var loadingTime = 0f;
#endif
                
                while (!asyncLoad.isDone)
                {
                    Evm.OnChangeProgressLoading.Dispatch(asyncLoad.progress, loadingSpeed);

#if ENABLE_ADS
                    //Check load ads
                    var canShowAoa = OnCanShowAoa?.Invoke() ?? false;
                    
                    if (GameManager.Instance.EnableAds && !canShowAoa)
                    {
                        loadingTime += Time.deltaTime;
                    }
                    else
                    {
                        loadingTime = 999f;
                    }

                    if (canShowAoa && !isShowAoa)
                    {
                        //Show AOA
                        isShowAoa = true;
                        onStartShowAoa.Dispatch();
                    }
#endif

                    if (asyncLoad.progress >= 0.90f
#if ENABLE_ADS
                        && loadingTime > loadingDuration
#endif
                        )
                    {
                        asyncLoad.allowSceneActivation = true;

                        if (asyncLoad.isDone)
                            break;
                    }

                    yield return null;
                }
            }

            GC.Collect(2, GCCollectionMode.Forced);
            Resources.UnloadUnusedAssets();
            
            float loadingTimeLeft =.1f / loadingSpeed +.1f;

            Evm.OnChangeProgressLoading.Dispatch(1f, loadingSpeed);
            yield return new WaitForSeconds(loadingTimeLeft);

            Evm.OnFinishLoadScene.Dispatch();

            if (_sceneLoadedCallback.ContainsKey(index))
            {
                _sceneLoadedCallback[index]?.Invoke();
            }
        }
    }
}