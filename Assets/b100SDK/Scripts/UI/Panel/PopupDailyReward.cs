using System;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.ExtraFeature.DailyReward;
using b100SDK.Scripts.ExtraFeature.DailyReward.UI;
using b100SDK.Scripts.Item.Claim;
using b100SDK.Scripts.UI.Components.Anim;
using b100SDK.Scripts.UI.Components.Button;
using b100SDK.Scripts.Utilities;
using b100SDK.Scripts.Utilities.Extensions;
using TMPro;
using UnityEngine;
using GameManager = b100SDK.Scripts.Base.GameManager;

namespace b100SDK.Scripts.UI.Panel
{
    public class PopupDailyReward : UIPanel
    {
        [Space(20f)]
        [SerializeField] 
        private DailyRewardItem[] dailyRewardItems;

        [SerializeField]
        private PanelAnim claimable;
        
        [SerializeField] 
        private PanelAnim notClaimable;
        
        [SerializeField] 
        private TMP_Text timer;

        [SerializeField]
        private GameItemClaim gameItemClaim;

        [SerializeField]
        private BhButton closeButton;

        [SerializeField]
        private BhButton skipButton;
        

        private bool _isNotClaim;
    
        public static PopupDailyReward Instance { get; private set; }

        public override string GetId()
        {
            return PanelItems.PopupDailyReward;
        }

        public static void Show()
        {
            var newInstance = (PopupDailyReward) GUIManager.Instance.NewPanel(PanelItems.PopupDailyReward);
            Instance = newInstance;
            newInstance.OnAppear();
        }

        public override void OnAppear()
        {
            if (isInited)
                return;

            base.OnAppear();

            Init();
        }
        
        public override void OnDisappear()
        {
            base.OnDisappear();
            Instance = null;
        }
        
        protected override void RegisterEvent()
        {
            base.RegisterEvent();
            
            closeButton.onClick.AddListener(Close);
            skipButton.onClick.AddListener(Skip);

            Evm.OnEverySecondTick.AddListener(UpdateTimer);
        }

        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
            
            closeButton.onClick.RemoveListener(Close);
            skipButton.onClick.RemoveListener(Skip);
        
            if (Evm)
                Evm.OnEverySecondTick.RemoveListener(UpdateTimer);
        }

        
        private void Init()
        {
            var rewardData = Cfg.gameCfg.extraFeatureConfig.dailyRewardConfig.GetRewardData();
            
            for (var i = 0; i < dailyRewardItems.Length; i++)
            {
                dailyRewardItems[i].Init(rewardData[i], Claim);
            }
        
            UpdateVisual();
        }


        void UpdateVisual()
        {
            for (var i = 0; i < dailyRewardItems.Length; i++)
            {
                dailyRewardItems[i].SetStatus(Gm.data.user.dailyRewardClaimStatusMap[i + 1]);
            }

            if (DailyRewardManager.Instance.IsClaimable())
            {
                claimable.gameObject.SetActiveWithChecker(true);
                claimable.Show();
                
                notClaimable.Hide(false,() =>
                {
                    notClaimable.gameObject.SetActiveWithChecker(false);
                });
                _isNotClaim = false;
            }
            else
            {
                claimable.Hide(false, () =>
                {
                    claimable.gameObject.SetActiveWithChecker(false);
                });
                
                notClaimable.gameObject.SetActiveWithChecker(true);
                notClaimable.Show();
                _isNotClaim = true;
            }
        }


        void Claim(DailyRewardData data)
        {
            // Claim logic

            foreach (var kvp in data.rewardData.QuantityMap)
            {
                gameItemClaim.Claim(kvp.Key, kvp.Value);
            }
            
            Gm.data.user.dailyRewardClaimStatusMap[data.day] = ClaimStatus.Claimed;
            UpdateVisual();
            BhDebug.Log("Claimed day: " + data.day);
        }


        void UpdateTimer()
        {
            if (_isNotClaim)
            {
                timer.text = DailyRewardManager.Instance.GetRemainTime().ToTimeFormatCompact();
            }
        }

        void Skip()
        {
            DailyRewardManager.Instance.SkipDay();
            UpdateVisual();
        }

    }
}