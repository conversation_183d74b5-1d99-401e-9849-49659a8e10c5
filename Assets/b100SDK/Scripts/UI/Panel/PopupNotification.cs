#region

using DG.Tweening;
using TMPro;

#endregion

namespace b100SDK.Scripts.UI.Panel
{
    public class PopupNotification : UIPanel
    {
        public TMP_Text txtMessage;

        public override string GetId()
        {
            return PanelItems.PopupNotification;
        }

        public static void Show(string message, float duration = 3f)
        {
            var newInstance = (PopupNotification) GUIManager.Instance.NewPanel(PanelItems.PopupNotification);
            newInstance.OnAppear(message, duration);
        }

        private void OnAppear(string message, float duration)
        {
            base.OnAppear();
            txtMessage.text = message;

            txtMessage.DOKill();
            DOVirtual.DelayedCall(duration, () => Close()).SetTarget(txtMessage);
        }
    }
}