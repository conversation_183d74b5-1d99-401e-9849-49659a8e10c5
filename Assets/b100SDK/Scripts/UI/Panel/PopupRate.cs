#region

using System.Collections.Generic;
using b100SDK.Scripts.Base;
using b100SDK.Scripts.UI.Components.Button;
using b100SDK.Scripts.Vibration;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

#if UNITY_IOS
using UnityEngine.iOS;
#endif

#endregion

namespace b100SDK.Scripts.UI.Panel
{
    public class PopupRate : UIPanel
    {
        private int _starCount = 4;

        [Space(20f)]
        [SerializeField]
        private BhButton closeButton;
        
        [SerializeField]
        private Image[] imgStar;

        [SerializeField]
        private Sprite selectedSprite, notSelectedSprite;

        [SerializeField]
        private BhButton confirmRateButton, cancelRateButton;
        
        [SerializeField]
        private List<BhButton> starButtons;

        [SerializeField]
        private float delayShowStar = .3f;

        [SerializeField]
        private float showStarInterval = 0.1f;

        public override string GetId()
        {
            return PanelItems.PopupRate;
        }

        public static void Show()
        {
            var newInstance = (PopupRate) GUIManager.Instance.NewPanel(PanelItems.PopupRate);
            newInstance.OnAppear();
        }

        public override void OnAppear()
        {
            if (isInited)
                return;

            base.OnAppear();

            Init();
        }
        
        protected override void RegisterEvent()
        {
            base.RegisterEvent();
        
            closeButton.onClick.AddListener(Close);
            confirmRateButton.onClick.AddListener(OnConfirmRate);
            cancelRateButton.onClick.AddListener(OnCancelRate);
            
            for (int i = 0; i < starButtons.Count; i++)
            {
                var index = i;
                starButtons[i].onClick.AddListener(() => OnClickRate(index));
            }
        }

        protected override void UnregisterEvent()
        {
            base.UnregisterEvent();
        
            closeButton.onClick.RemoveListener(Close);
            confirmRateButton.onClick.RemoveListener(OnConfirmRate);
            cancelRateButton.onClick.RemoveListener(OnCancelRate);
            
            for (int i = 0; i < starButtons.Count; i++)
            {
                var i1 = i;
                starButtons[i].onClick.RemoveListener(() => OnClickRate(i1));
            }
        }
        
        
        void Init()
        {
            confirmRateButton.Hide(true);
            cancelRateButton.Hide(true);

            this.DOKill();

            SetStar(0);
        
            var sequence = DOTween.Sequence();
            sequence.AppendInterval(delayShowStar);
        
            for (int i = 1; i <= 5; i++)
            {
                var i1 = i;
                sequence.AppendCallback(() => SetStar(i1));
                sequence.AppendInterval(showStarInterval);
            }

            sequence.AppendCallback(() =>
            {
                confirmRateButton.Show();
                cancelRateButton.Show();
            });

            sequence.SetTarget(this);

            sequence.Play();
        }
    
    
        [Button]
        void SetStar(int starValue)
        {
            for (var i = 0; i < imgStar.Length; i++)
            {
                imgStar[i].sprite = i < starValue ? selectedSprite : notSelectedSprite;
            }
        }

        void OnClickRate(int index)
        {
            BhVibrate.Haptic(BhHapticTypes.SoftImpact);
            _starCount = index;
            SetStar(index + 1);
        }

        void OnConfirmRate()
        {
            Close();

            if (_starCount < 4)
            {
                PopupNotification.Show(GameConst.FEEDBACK_THANKS);
            }
            else
            {
#if UNITY_ANDROID
                Application.OpenURL(@"https://play.google.com/store/apps/details?id=" + Cfg.gameSetting.packageName);
#elif UNITY_IOS
        if (!Device.RequestStoreReview())
        {
            Application.OpenURL(@"https://apps.apple.com/us/app/id" + Cfg.gameSetting.appstoreId);
        }
#else
            Debug.Log("Rated in store!");
#endif
            }

            Gm.data.user.rated = true;
            Database.SaveData();
        }

        void OnCancelRate()
        {
            Close();
        }
    }
}