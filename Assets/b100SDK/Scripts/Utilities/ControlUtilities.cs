using System.Collections.Generic;
using b100SDK.Scripts.Base;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace b100SDK.Scripts.Utilities
{
    public static class ControlUtilities
    {
        public static T GetItemOverlapOnUI<T>(GraphicRaycaster raycaster, Vector3 position, bool needTranslateToScreenPoint = false) where T : MonoBehaviour
        {
            T result = null;
            
            var allItem = new List<RaycastResult>();
                
            PointerEventData pointerEventData = new PointerEventData(EventSystem.current)
            {
                position = needTranslateToScreenPoint ? RectTransformUtility.WorldToScreenPoint(Camera.current, position) : position
            };
            
            raycaster.Raycast(pointerEventData, allItem);
            
            foreach (var item in allItem)
            { 
                item.gameObject.TryGetComponent<T>(out var itemResult);

                if (itemResult)
                {
                    result = itemResult;
                    break;
                } 
            }
            
            return result;
        }
        
        public static List<T> GetItemsOverlapOnUI<T>(GraphicRaycaster raycaster, Vector3 position, bool needTranslateToScreenPoint = false) where T : MonoBehaviour
        {
            List<T> result = new();
            
            var allItem = new List<RaycastResult>();
                
            PointerEventData pointerEventData = new PointerEventData(EventSystem.current)
            {
                position = RectTransformUtility.WorldToScreenPoint(Camera.current, position)
            };
            
            raycaster.Raycast(pointerEventData, allItem);
            
            foreach (var item in allItem)
            { 
                item.gameObject.TryGetComponent<T>(out var itemResult);

                if (itemResult)
                {
                    result.Add(itemResult);
                } 
            }
            
            return result;
        }
        
        
        
        private static RaycastHit2D[] _raycastHit2Ds = new RaycastHit2D[5];
        
        public static List<T> Raycast2D<T>(Vector3 position, float distance, LayerMask layerMask) where T : MonoBehaviour
        {
            var result = new List<T>();
            
            var hitCount = Physics2D.RaycastNonAlloc(position, Vector2.zero, _raycastHit2Ds, distance, layerMask);
            for (var i = 0; i < hitCount; i++)
            {
                var hit = _raycastHit2Ds[i];
                hit.collider.gameObject.TryGetComponent<T>(out var itemResult);

                if (itemResult)
                {
                    result.Add(itemResult);
                } 
            }
            
            return result;
        }
        
        public static T Raycast2D<T>(Vector3 position) where T : MonoBehaviour
        {
            T result = null;
            
            var hitCount = Physics2D.RaycastNonAlloc(Camera.main.ScreenToWorldPoint(position), Vector2.up, _raycastHit2Ds);
            BhDebug.Log("Count: " + hitCount);
            for (var i = 0; i < hitCount; i++)
            {
                var hit = _raycastHit2Ds[i];
                hit.collider.gameObject.TryGetComponent<T>(out var itemResult);

                if (itemResult)
                {
                    result = itemResult;
                    break;
                } 
            }
            
            return result;
        }
    }
}