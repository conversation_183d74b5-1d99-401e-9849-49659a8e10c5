#region

using System;
using System.Text;
using System.Linq;

#endregion

namespace b100SDK.Scripts.Utilities.Extensions
{
    public static class  StringExtensions
    {


        #region Enum Handler

        
        public static T ToEnum<T>(this string value)
        {
            try
            {
                return (T) Enum.Parse(typeof(T), value, true);
            }
            catch (Exception)
            {
                return (T) Enum.Parse(typeof(T), "None", true);
            }
        }
        
        

        public static bool IsEnum<T>(this string value)
        {
            return !Equals(value.ToEnum<T>(), "None");
        }
        
        #endregion

        

        #region Prefix Handler

        
        public static string GetPrefix(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";

            var index = value.IndexOf('_');
            if (index < 0)
                return "";

            return value.Substring(0, index);
        }
        
        

        public static string GetContent(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";

            var index = value.IndexOf('_');
            if (index < 0)
                return "";

            return value.Substring(index + 1, value.Length - index - 1);
        }

        #endregion



        #region Time
        

        public static string ToTimeFormat(this int seconds)
        {
            return ToTimeFormat((long)seconds);
        }

        public static string ToTimeFormatCompact(this int seconds)
        {
            return ToTimeFormatCompact((long)seconds);
        }

        public static string ToTimeFormat(this long seconds)
        {
            if (seconds <= 0)
                return "";

            TimeSpan t = TimeSpan.FromSeconds(seconds);

            string formatTime;

            if (seconds >= 3600)
            {
                formatTime = string.Format("{0:D1}h {1:D2}m",
                    t.Hours,
                    t.Minutes);
            }
            else if (seconds >= 60)
            {
                formatTime = string.Format("{0:D2}m {1:D2}s",
                    t.Minutes,
                    t.Seconds);
            }
            else
            {
                formatTime = string.Format("{0:D1}s",
                    t.Seconds);
            }

            return formatTime;
        }

        public static string ToTimeFormatCompact(this long seconds)
        {
            if (seconds < 0)
                return "";

            TimeSpan t = TimeSpan.FromSeconds(seconds);

            string formatTime;

            if (seconds >= 3600)
            {
                formatTime = string.Format("{0:D1}:{1:D2}:{2:D2}",
                    t.Hours + t.Days * 24,
                    t.Minutes,
                    t.Seconds);
            }
            else
            {
                formatTime = string.Format("{0:D2}:{1:D2}",
                    t.Minutes,
                    t.Seconds);
            }

            return formatTime;
        }

        #endregion
        
        
        
        
        

        public static string ToCountFormat(int value, int digits)
        {
            var s = value.ToString();
            var originLength = s.Length;
            if (originLength >= digits)
                return s;

            for (var i = 0; i < digits - originLength; i++)
                s = "0" + s;

            return s;
        }

        public static string ToFormatString(this long s)
        {
            return $"{s:n0}";
        }

        public static string ToFormatString(this int s)
        {
            return $"{s:n0}";
        }

        public static string ToFormatString(this double s)
        {
            return $"{s:n0}";
        }

        public static string ToQuantityString(this long s)
        {
            return $"x{s:n0}";
        }

        public static string ToQuantityString(this int s)
        {
            return $"x{s:n0}";
        }

        public static string ToQuantityString(this double s)
        {
            return $"x{s:n0}";
        }




        #region Other

        public static string StringReplaceAt(string value, int index, char newchar)
        {
            if (value.Length <= index)
                return value;
            var sb = new StringBuilder(value);
            sb[index] = newchar;
            return sb.ToString();
        }

        public static string SplitPascalCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;
            
            StringBuilder result = new StringBuilder();
            result.Append(input[0]); // Giữ nguyên ký tự đầu tiên
            
            // Bắt đầu từ ký tự thứ 2
            for (int i = 1; i < input.Length; i++)
            {
                if (char.IsUpper(input[i])) // Nếu gặp chữ hoa
                {
                    // Thêm khoảng trắng trước chữ hoa nếu:
                    // 1. Ký tự trước nó là chữ thường, hoặc
                    // 2. Ký tự sau nó là chữ thường (nếu có)
                    if (char.IsLower(input[i - 1]) || 
                        (i < input.Length - 1 && char.IsLower(input[i + 1])))
                    {
                        result.Append(' ');
                    }
                }
                result.Append(input[i]);
            }
            
            return result.ToString();
        }

        #endregion

        #region String Case Conversions

        /// <summary>
        /// Converts "this_is_snake_case" to "ThisIsSnakeCase"
        /// </summary>
        public static string ToTitleCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var words = input.Split(new[] { '_', ' ', '-' }, StringSplitOptions.RemoveEmptyEntries);
            return string.Join("", words.Select(word => 
                char.ToUpper(word[0]) + word.Substring(1).ToLower()));
        }

        /// <summary>
        /// Converts "ThisIsPascalCase" to "this_is_pascal_case"
        /// </summary>
        public static string ToSnakeCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            StringBuilder result = new StringBuilder();
            result.Append(char.ToLower(input[0]));

            for (int i = 1; i < input.Length; i++)
            {
                if (char.IsUpper(input[i]))
                {
                    if ((i > 0 && !char.IsUpper(input[i - 1])) || 
                        (i + 1 < input.Length && !char.IsUpper(input[i + 1])))
                    {
                        result.Append('_');
                    }
                    result.Append(char.ToLower(input[i]));
                }
                else
                {
                    result.Append(input[i]);
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// Converts "ThisIsPascalCase" to "thisIsCamelCase"
        /// </summary>
        public static string ToCamelCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            if (input.Length == 1)
                return input.ToLower();

            return char.ToLower(input[0]) + input.Substring(1);
        }

        /// <summary>
        /// Converts "ThisIsPascalCase" or "thisIsCamelCase" to "This Is Pascal Case"
        /// </summary>
        public static string ToTitleCaseWithSpaces(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            string pascalCase = input;
            if (char.IsLower(input[0])) // If camelCase, convert to PascalCase first
            {
                pascalCase = char.ToUpper(input[0]) + input.Substring(1);
            }

            return SplitPascalCase(pascalCase);
        }

        /// <summary>
        /// Converts "this-is-kebab-case" to "ThisIsPascalCase"
        /// </summary>
        public static string FromKebabCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var words = input.Split('-');
            return string.Join("", words.Select(word => 
                char.ToUpper(word[0]) + word.Substring(1).ToLower()));
        }

        /// <summary>
        /// Converts "ThisIsPascalCase" to "this-is-kebab-case"
        /// </summary>
        public static string ToKebabCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            StringBuilder result = new StringBuilder();
            result.Append(char.ToLower(input[0]));

            for (int i = 1; i < input.Length; i++)
            {
                if (char.IsUpper(input[i]))
                {
                    if ((i > 0 && !char.IsUpper(input[i - 1])) || 
                        (i + 1 < input.Length && !char.IsUpper(input[i + 1])))
                    {
                        result.Append('-');
                    }
                    result.Append(char.ToLower(input[i]));
                }
                else
                {
                    result.Append(input[i]);
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// Converts "THIS_IS_CONSTANT_CASE" to "ThisIsPascalCase"
        /// </summary>
        public static string FromConstantCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var words = input.Split('_');
            return string.Join("", words.Select(word => 
                char.ToUpper(word[0]) + word.Substring(1).ToLower()));
        }

        /// <summary>
        /// Converts "ThisIsPascalCase" to "THIS_IS_CONSTANT_CASE"
        /// </summary>
        public static string ToConstantCase(this string input)
        {
            return ToSnakeCase(input).ToUpper();
        }

        /// <summary>
        /// Converts any case to sentence case: "This is a normal sentence"
        /// </summary>
        public static string ToSentenceCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            // First convert to space-separated words
            string withSpaces = ToTitleCaseWithSpaces(input);
            
            // Then convert to sentence case
            return char.ToUpper(withSpaces[0]) + withSpaces.Substring(1).ToLower();
        }

        /// <summary>
        /// Makes first character uppercase
        /// </summary>
        public static string FirstCharToUpper(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            return char.ToUpper(input[0]) + input.Substring(1);
        }

        /// <summary>
        /// Makes first character lowercase
        /// </summary>
        public static string FirstCharToLower(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            return char.ToLower(input[0]) + input.Substring(1);
        }

        #endregion

        #region String Manipulation

        /// <summary>
        /// Removes all special characters and spaces, keeps only letters and numbers
        /// </summary>
        public static string RemoveSpecialCharacters(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            StringBuilder sb = new StringBuilder();
            foreach (char c in input)
            {
                if ((c >= '0' && c <= '9') || (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z'))
                {
                    sb.Append(c);
                }
            }
            return sb.ToString();
        }

        /// <summary>
        /// Truncates string to specified length and adds ellipsis if truncated
        /// </summary>
        public static string Truncate(this string input, int maxLength, string suffix = "...")
        {
            if (string.IsNullOrEmpty(input) || maxLength <= 0)
                return input;

            if (input.Length <= maxLength)
                return input;

            return input.Substring(0, maxLength - suffix.Length) + suffix;
        }

        /// <summary>
        /// Ensures string starts with prefix
        /// </summary>
        public static string EnsureStartsWith(this string input, string prefix)
        {
            if (string.IsNullOrEmpty(input)) return prefix;
            return input.StartsWith(prefix) ? input : prefix + input;
        }

        /// <summary>
        /// Ensures string ends with suffix
        /// </summary>
        public static string EnsureEndsWith(this string input, string suffix)
        {
            if (string.IsNullOrEmpty(input)) return suffix;
            return input.EndsWith(suffix) ? input : input + suffix;
        }

        /// <summary>
        /// Removes prefix if exists
        /// </summary>
        public static string RemovePrefix(this string input, string prefix)
        {
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(prefix))
                return input;

            if (input.StartsWith(prefix))
                return input.Substring(prefix.Length);

            return input;
        }

        /// <summary>
        /// Removes suffix if exists
        /// </summary>
        public static string RemoveSuffix(this string input, string suffix)
        {
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(suffix))
                return input;

            if (input.EndsWith(suffix))
                return input.Substring(0, input.Length - suffix.Length);

            return input;
        }

        /// <summary>
        /// Reverses a string
        /// </summary>
        public static string Reverse(this string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            char[] chars = input.ToCharArray();
            Array.Reverse(chars);
            return new string(chars);
        }

        /// <summary>
        /// Repeats a string n times
        /// </summary>
        public static string Repeat(this string input, int count)
        {
            if (string.IsNullOrEmpty(input) || count <= 0) return string.Empty;
            return new StringBuilder(input.Length * count)
                .Insert(0, input, count)
                .ToString();
        }

        #endregion

        #region String Validation

        /// <summary>
        /// Checks if string is valid email
        /// </summary>
        public static bool IsValidEmail(this string input)
        {
            if (string.IsNullOrEmpty(input)) return false;
            try
            {
                var addr = new System.Net.Mail.MailAddress(input);
                return addr.Address == input;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Checks if string contains only numbers
        /// </summary>
        public static bool IsNumeric(this string input)
        {
            if (string.IsNullOrEmpty(input)) return false;
            return input.All(char.IsDigit);
        }

        /// <summary>
        /// Checks if string contains only letters
        /// </summary>
        public static bool IsAlpha(this string input)
        {
            if (string.IsNullOrEmpty(input)) return false;
            return input.All(char.IsLetter);
        }

        /// <summary>
        /// Checks if string contains only letters and numbers
        /// </summary>
        public static bool IsAlphanumeric(this string input)
        {
            if (string.IsNullOrEmpty(input)) return false;
            return input.All(c => char.IsLetterOrDigit(c));
        }

        #endregion

        #region String Formatting

        /// <summary>
        /// Formats file size to human readable string
        /// </summary>
        public static string ToFileSizeString(this long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1) {
                order++;
                len = len/1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// Formats number with ordinal suffix (1st, 2nd, 3rd, etc)
        /// </summary>
        public static string ToOrdinalString(this int num)
        {
            if (num <= 0) return num.ToString();

            switch (num % 100)
            {
                case 11:
                case 12:
                case 13:
                    return num + "th";
            }

            switch (num % 10)
            {
                case 1:
                    return num + "st";
                case 2:
                    return num + "nd";
                case 3:
                    return num + "rd";
                default:
                    return num + "th";
            }
        }

        /// <summary>
        /// Formats number to K/M/B format (1K, 1M, 1B)
        /// </summary>
        public static string ToShortNumberString(this long num)
        {
            if (num < 1000) return num.ToString();
            if (num < 1000000) return $"{num/1000.0:0.#}K";
            if (num < 1000000000) return $"{num/1000000.0:0.#}M";
            return $"{num/1000000000.0:0.#}B";
        }

        #endregion

        #region String Security

        /// <summary>
        /// Creates a SHA256 hash of the string
        /// </summary>
        public static string ToSHA256(this string input)
        {
            if (string.IsNullOrEmpty(input)) return string.Empty;
            
            using (var sha = System.Security.Cryptography.SHA256.Create())
            {
                var bytes = System.Text.Encoding.UTF8.GetBytes(input);
                var hash = sha.ComputeHash(bytes);
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// Creates an MD5 hash of the string
        /// </summary>
        public static string ToMD5(this string input)
        {
            if (string.IsNullOrEmpty(input)) return string.Empty;
            
            using (var md5 = System.Security.Cryptography.MD5.Create())
            {
                var bytes = System.Text.Encoding.UTF8.GetBytes(input);
                var hash = md5.ComputeHash(bytes);
                return BitConverter.ToString(hash).Replace("-", "").ToLower();
            }
        }

        #endregion
    }
}