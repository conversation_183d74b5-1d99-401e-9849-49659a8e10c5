using System.IO;
using Newtonsoft.Json;
using UnityEngine;

namespace b100SDK.Scripts.Utilities
{
    public static class LocalDataUtilities
    {
        public static void SaveData<T>(this T data, string fileName, string folderPath = "") where T : class
        {
            // Create folder if not exist
            if (!string.IsNullOrEmpty(folderPath) && !Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }
            
            // Create file path
            var correctFilePath = string.IsNullOrEmpty(folderPath)
                ? Path.Combine(Application.persistentDataPath, fileName)
                : folderPath + "/" + fileName;
            
            string jsonData = JsonConvert.SerializeObject(data, Formatting.Indented);
                
            File.WriteAllText(correctFilePath, jsonData);
        }

        public static T LoadData<T>(string fileName, string folderPath = "") where T : class
        {
            // Create folder if not exist
            if (!string.IsNullOrEmpty(folderPath) && !Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            // Create file path
            var correctFilePath = string.IsNullOrEmpty(folderPath)
                ? Path.Combine(Application.persistentDataPath, fileName)
                : folderPath + "/" + fileName;

            if (!File.Exists(correctFilePath))
            {
                return null;
            }
            
            string jsonData = File.ReadAllText(correctFilePath);
            return JsonConvert.DeserializeObject<T>(jsonData);
        }

        public static bool DeleteData(string fileName, string folderPath = "")
        {
            // Create file path
            var correctFilePath = string.IsNullOrEmpty(folderPath)
                ? Path.Combine(Application.persistentDataPath, fileName)
                : folderPath + "/" + fileName;
            
            // Check if file exists
            if (File.Exists(correctFilePath))
            {
                File.Delete(correctFilePath);
                return true;
            }
            
            return false;
        }
    }
}