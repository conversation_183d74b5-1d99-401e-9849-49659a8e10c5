using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Android;

namespace b100SDK.Scripts.Utilities
{
    public class PermissionUtils
    {
        private static readonly Dictionary<PermissionType, string> AndroidPermissionMap =
            new Dictionary<PermissionType, string>()
            {
                { PermissionType.Camera, Permission.Camera },
                { PermissionType.Microphone, Permission.Microphone },
                { PermissionType.Location, Permission.FineLocation },
                { PermissionType.Storage, Permission.ExternalStorageWrite },
                { PermissionType.Contacts, "android.permission.READ_CONTACTS" },
                { PermissionType.PhoneState, "android.permission.READ_PHONE_STATE" }
            };


        public static bool HasPermission(PermissionType permissionType)
        {
            return Permission.HasUserAuthorizedPermission(AndroidPermissionMap[permissionType]);
        }
        public static void RequestPermission(PermissionType permissionType, Action<string> onGranted, Action<string> onDenied, Action<string> onDeniedAndNeverAskAgain)
        {
            var callback = new PermissionCallbacks();
            callback.PermissionGranted += onGranted;
            callback.PermissionDenied += onDenied;
            callback.PermissionDeniedAndDontAskAgain += onDeniedAndNeverAskAgain;
            
            Permission.RequestUserPermission(AndroidPermissionMap[permissionType], callback);
        }
        
        public static void OpenAppSettings()
        {
#if UNITY_ANDROID
            using (AndroidJavaObject activity = GetCurrentActivity())
            {
                using (AndroidJavaObject intent = new AndroidJavaObject("android.content.Intent", "android.settings.APPLICATION_DETAILS_SETTINGS"))
                {
                    using (AndroidJavaClass uriClass = new AndroidJavaClass("android.net.Uri"))
                    {
                        AndroidJavaObject uri = uriClass.CallStatic<AndroidJavaObject>("parse", "package:" + Application.identifier);
                        intent.Call<AndroidJavaObject>("setData", uri);
                        activity.Call("startActivity", intent);
                    }
                }
            }
#endif
        }
        
        private static AndroidJavaObject GetCurrentActivity()
        {
            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            return unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
        }
    }
    
    public enum PermissionType
    {
        Camera, 
        Microphone,
        Location,
        Storage,
        Contacts,
        PhoneState,
    }

    public enum RequestPermissionStatus
    {
        NotRequested,
        Granted,
        Denied,
        DeniedAndNeverAskAgain
    }
}