using b100SDK.Scripts.Base;
using Lofelt.NiceVibrations;

namespace b100SDK.Scripts.Vibration
{
    public static class BhVibrate
    {
        public static void Haptic(BhHapticTypes type)
        {
            if (!GameManager.Instance.data.setting.hapticEnable)
                return;

            HapticPatterns.PlayPreset((HapticPatterns.PresetType) type);
        }

        public static void ContinuousHaptic(float continuousAmplitude = 0.05f, float continuousFrequency = 0.05f, float continuousDuration = 0.05f)
        {
            if (!GameManager.Instance.data.setting.hapticEnable)
                return;

            HapticPatterns.PlayConstant(continuousAmplitude, continuousFrequency, continuousDuration);
        }

        public static void EmphasisHaptic(float continuousAmplitude = 0.05f, float continuousFrequency = 0.05f)
        {
            if (!GameManager.Instance.data.setting.hapticEnable)
                return;

            HapticPatterns.PlayEmphasis(continuousAmplitude, continuousFrequency);
        }
    }
    
    public enum BhHapticTypes
    {
        Selection = 0,
        Success = 1,
        Warning = 2,
        Failure = 3,
        LightImpact = 4,
        MediumImpact = 5,
        HeavyImpact = 6,
        RigidImpact = 7,
        SoftImpact = 8,
        None = -1
    }
}


